import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useEditorStore, PANEL_TYPES } from '../store/editorStore'
import ElementsPanel from './panels/ElementsPanel'
import LayersPanel from './panels/LayersPanel'
import PagesPanel from './panels/PagesPanel'
import ThemePanel from './panels/ThemePanel'
import MediaPanel from './panels/MediaPanel'

const LeftPanel = () => {
  const { activePanel } = useEditorStore()

  const panels = {
    [PANEL_TYPES.ELEMENTS]: {
      title: '添加元素',
      component: ElementsPanel,
    },
    [PANEL_TYPES.LAYERS]: {
      title: '图层管理',
      component: LayersPanel,
    },
    [PANEL_TYPES.PAGES]: {
      title: '页面管理',
      component: PagesPanel,
    },
    [PANEL_TYPES.THEME]: {
      title: '主题设置',
      component: ThemePanel,
    },
    [PANEL_TYPES.MEDIA]: {
      title: '媒体库',
      component: MediaPanel,
    },
  }

  const currentPanel = panels[activePanel]
  const PanelComponent = currentPanel?.component

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* 面板标题 */}
      <div className="h-14 px-6 flex items-center border-b border-gray-200 bg-gray-50">
        <h2 className="font-semibold text-gray-900">{currentPanel?.title}</h2>
      </div>

      {/* 面板内容 */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {PanelComponent && (
            <motion.div
              key={activePanel}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="h-full"
            >
              <PanelComponent />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default LeftPanel
