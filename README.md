# 🎨 可视化编辑器 (Visual Editor)

一个基于 React 18 + Vite 构建的现代化可视化页面编辑器，支持拖拽式组件编辑、实时预览和响应式设计。

## ✨ 功能特性

### 🎨 核心编辑功能
- **拖拽式编辑**: 使用 React DnD 实现的流畅拖拽体验
- **实时预览**: 所见即所得的编辑体验
- **响应式设计**: 支持桌面、平板、手机三种断点预览
- **缩放控制**: 支持 50%-200% 的画布缩放

### 📱 响应式断点
- **桌面端**: 1200×800px
- **平板端**: 768×1024px
- **手机端**: 375×667px

### 🧩 丰富的组件库
- **文本组件**: 可编辑的文本内容，支持富文本样式
- **图片组件**: 支持自定义图片和媒体库选择
- **按钮组件**: 可自定义样式和链接的交互按钮
- **容器组件**: 用于布局的容器元素
- **表单组件**: 动态表单字段，支持多种输入类型
- **视频组件**: 支持视频播放和控制选项
- **画廊组件**: 可配置的图片网格展示
- **地图组件**: 地图展示组件

### 🎛️ 管理面板

#### 元素面板
- 8种预设组件类型
- 拖拽添加到画布
- 组件使用提示和快捷键说明

#### 图层面板
- 显示页面元素层级结构
- 支持显示/隐藏切换
- 支持锁定/解锁操作
- 点击图层直接选中元素
- 按 Z-index 排序显示

#### 页面面板
- 多页面管理系统
- 添加、切换、删除页面
- 页面统计信息
- 每个页面独立的元素管理

#### 主题面板
- **颜色方案**: 预设颜色 + 自定义颜色选择器
- **字体设置**: 多种字体族选择
- **预设主题**: 现代、经典、极简三种风格
- **实时预览**: 主题更改即时生效

#### 媒体面板
- 文件上传功能（拖拽或点击）
- 媒体库管理
- 支持图片和视频格式
- 拖拽使用媒体资源

### ⚙️ 属性编辑系统

#### 样式面板
- **颜色设置**: 背景色、文字色、边框色
- **字体设置**: 字体族、大小、粗细
- **边框设置**: 宽度、圆角、样式
- **阴影效果**: 可调节的阴影强度
- **透明度控制**: 0-100% 透明度调节

#### 布局面板
- **位置控制**: X/Y 坐标精确定位
- **尺寸设置**: 宽度/高度设置
- **间距控制**: 外边距/内边距四方向设置
- **层级管理**: Z-index 控制
- **显示状态**: 显示/隐藏、锁定/解锁
- **变换效果**: 旋转角度设置

#### 内容面板
- **文本内容**: 多行文本编辑、对齐方式
- **图片设置**: URL、替代文本、适应方式
- **按钮配置**: 文本、链接、打开方式
- **视频选项**: 控制栏、自动播放、循环
- **表单字段**: 动态添加/删除字段
- **画廊配置**: 列数、间距设置
- **地图设置**: 位置、缩放级别

### 💾 数据管理
- **自动保存**: 每30秒自动保存到本地存储
- **历史记录**: 支持撤销/重做操作 (最多50步)
- **本地存储**: 数据持久化保存
- **实时同步**: 状态变更即时保存

### ⌨️ 快捷键支持
- `Ctrl+Z` / `Cmd+Z`: 撤销操作
- `Ctrl+Y` / `Cmd+Y`: 重做操作
- `Ctrl+S` / `Cmd+S`: 手动保存
- `Delete` / `Backspace`: 删除选中元素

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn 或 pnpm

### 安装依赖

⚠️ **重要**: 如果遇到依赖冲突错误，请查看 [INSTALL.md](./INSTALL.md) 获取详细的解决方案。

```bash
# 推荐使用 --legacy-peer-deps 标志
npm install --legacy-peer-deps

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 http://localhost:3000 开始使用编辑器。

### 构建生产版本
```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

### 预览生产版本
```bash
npm run preview
# 或
yarn preview
# 或
pnpm preview
```

## 🛠️ 技术栈

### 前端框架
- **React 18.2**: 现代 React 版本，支持并发特性和 Suspense
- **Vite**: 快速的构建工具和开发服务器
- **JavaScript ES6+**: 现代 JavaScript 语法

### 状态管理
- **Zustand**: 轻量级状态管理库
- **React DnD**: 拖拽功能实现

### UI 组件库
- **Radix UI**: 无样式的可访问组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Framer Motion**: 动画库
- **Lucide React**: 图标库

### 开发工具
- **ESLint**: 代码质量检查
- **PostCSS**: CSS 处理工具
- **Autoprefixer**: CSS 前缀自动添加

## 📁 项目结构

```
src/
├── components/           # React 组件
│   ├── panels/          # 左侧面板组件
│   │   ├── ElementsPanel.jsx
│   │   ├── LayersPanel.jsx
│   │   ├── PagesPanel.jsx
│   │   ├── ThemePanel.jsx
│   │   └── MediaPanel.jsx
│   ├── properties/      # 右侧属性面板组件
│   │   ├── StylePanel.jsx
│   │   ├── LayoutPanel.jsx
│   │   └── ContentPanel.jsx
│   ├── Canvas.jsx       # 画布组件
│   ├── CanvasElement.jsx # 画布元素组件
│   ├── LeftPanel.jsx    # 左侧面板容器
│   ├── LeftSidebar.jsx  # 左侧工具栏
│   ├── RightPanel.jsx   # 右侧面板容器
│   └── TopToolbar.jsx   # 顶部工具栏
├── hooks/               # 自定义 Hooks
│   └── useAutoSave.js   # 自动保存 Hook
├── store/               # 状态管理
│   └── editorStore.js   # 编辑器状态
├── App.jsx              # 主应用组件
├── main.jsx             # 应用入口
└── index.css            # 全局样式
```

## 🎯 使用指南

### 1. 基本操作
1. 从左侧工具栏选择功能面板
2. 拖拽元素到中央画布
3. 点击元素进行选中和编辑
4. 在右侧属性面板调整样式

### 2. 添加元素
- 点击左侧 "+" 图标打开元素面板
- 选择需要的组件类型
- 拖拽到画布中的目标位置
- 元素会自动添加到图层列表

### 3. 编辑属性
- 点击画布中的元素进行选中
- 在右侧属性面板的三个标签页中修改：
  - **样式**: 颜色、字体、边框等视觉样式
  - **布局**: 位置、尺寸、间距等布局属性
  - **内容**: 文本、链接、媒体等内容设置

### 4. 响应式设计
- 点击顶部断点按钮切换设备预览
- 在不同断点下调整布局
- 确保在各设备上的显示效果

### 5. 主题定制
- 点击左侧主题图标
- 选择预设颜色或使用颜色选择器
- 应用预设主题风格
- 更改会实时应用到页面

### 6. 媒体管理
- 点击左侧媒体图标
- 上传本地图片或视频文件
- 拖拽媒体文件到画布使用

## 🔧 自定义开发

### 添加新组件类型
1. 在 `store/editorStore.js` 中添加新的 `ELEMENT_TYPES`
2. 在 `components/panels/ElementsPanel.jsx` 中添加组件项
3. 在 `components/CanvasElement.jsx` 中添加渲染逻辑
4. 在属性面板中添加对应的编辑选项

### 扩展属性面板
1. 在对应的属性面板文件中添加新的属性字段
2. 确保属性变更能正确更新到状态管理
3. 添加必要的验证和默认值处理

### 自定义主题
1. 在 `components/panels/ThemePanel.jsx` 中添加新的预设
2. 定义主题的颜色、字体等样式规则
3. 确保主题能正确应用到所有元素

## 🌟 未来规划
- [ ] 组件库扩展 (图表、表格、轮播等)
- [ ] 动画效果系统
- [ ] 云端存储集成
- [ ] 实时协作编辑
- [ ] 模板市场
- [ ] SEO 优化工具
- [ ] 代码导出优化
- [ ] 插件系统
- [ ] 移动端适配
- [ ] 国际化支持

## 📄 许可证
MIT License

## 🤝 贡献指南
欢迎提交 Issue 和 Pull Request 来改进这个项目！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request
