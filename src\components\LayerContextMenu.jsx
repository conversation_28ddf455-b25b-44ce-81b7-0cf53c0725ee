import React, { useState, useEffect } from 'react'
import { useEditorStore } from '../store/editorStore'
import {
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  Copy,
  Trash2,
  Eye,
  <PERSON>Off,
  Lock,
  Unlock
} from 'lucide-react'

const LayerContextMenu = ({ elementId, position, onClose }) => {
  const {
    bringToFront,
    sendToBack,
    moveForward,
    moveBackward,
    deleteElement,
    updateElement,
    selectedElementData
  } = useEditorStore()

  const [element, setElement] = useState(null)

  useEffect(() => {
    if (elementId) {
      setElement(selectedElementData)
    }
  }, [elementId, selectedElementData])

  const handleAction = (action) => {
    switch (action) {
      case 'bringToFront':
        bringToFront(elementId)
        break
      case 'sendToBack':
        sendToBack(elementId)
        break
      case 'moveForward':
        moveForward(elementId)
        break
      case 'moveBackward':
        moveBackward(elementId)
        break
      case 'copy':
        // TODO: 实现复制功能
        console.log('Copy element:', elementId)
        break
      case 'delete':
        deleteElement(elementId)
        break
      case 'toggleVisible':
        updateElement(elementId, { visible: !element?.visible })
        break
      case 'toggleLocked':
        updateElement(elementId, { locked: !element?.locked })
        break
    }
    onClose()
  }

  const menuItems = [
    {
      label: '置于顶层',
      icon: ChevronUp,
      action: 'bringToFront',
      shortcut: 'Ctrl+Shift+]'
    },
    {
      label: '上移一层',
      icon: ArrowUp,
      action: 'moveForward',
      shortcut: 'Ctrl+]'
    },
    {
      label: '下移一层',
      icon: ArrowDown,
      action: 'moveBackward',
      shortcut: 'Ctrl+['
    },
    {
      label: '置于底层',
      icon: ChevronDown,
      action: 'sendToBack',
      shortcut: 'Ctrl+Shift+['
    },
    { type: 'divider' },
    {
      label: '复制',
      icon: Copy,
      action: 'copy',
      shortcut: 'Ctrl+C'
    },
    {
      label: '删除',
      icon: Trash2,
      action: 'delete',
      shortcut: 'Delete',
      danger: true
    },
    { type: 'divider' },
    {
      label: element?.visible ? '隐藏' : '显示',
      icon: element?.visible ? EyeOff : Eye,
      action: 'toggleVisible'
    },
    {
      label: element?.locked ? '解锁' : '锁定',
      icon: element?.locked ? Unlock : Lock,
      action: 'toggleLocked'
    }
  ]

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.context-menu')) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  if (!position) return null

  return (
    <div
      className="context-menu fixed bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[200px] z-[1000]"
      style={{
        left: position.x,
        top: position.y
      }}
    >
      {menuItems.map((item, index) => {
        if (item.type === 'divider') {
          return <div key={index} className="border-t border-gray-100 my-1" />
        }

        const Icon = item.icon

        return (
          <button
            key={index}
            className={`
              w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center justify-between
              ${item.danger ? 'text-red-600 hover:bg-red-50' : 'text-gray-700'}
            `}
            onClick={() => handleAction(item.action)}
          >
            <div className="flex items-center space-x-3">
              <Icon className="w-4 h-4" />
              <span>{item.label}</span>
            </div>
            {item.shortcut && (
              <span className="text-xs text-gray-400">{item.shortcut}</span>
            )}
          </button>
        )
      })}
      
      {element && (
        <>
          <div className="border-t border-gray-100 my-1" />
          <div className="px-4 py-2 text-xs text-gray-500">
            <div>层级: {element.zIndex || 1}</div>
            <div>位置: ({Math.round(element.margin?.left || 0)}, {Math.round(element.margin?.top || 0)})</div>
            {element.size && (
              <div>尺寸: {element.size.width} × {element.size.height}</div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default LayerContextMenu
