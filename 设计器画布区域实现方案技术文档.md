# 设计器画布区域实现方案技术文档

## 1. 概述

本文档详细分析设计器画布区域的实现方案，包括元素与选中框之间的联系、定位方案、布局方案等核心技术要点。

## 2. 整体架构设计

### 2.1 分层架构

设计器画布采用多层渲染架构，确保不同功能模块的独立性和可维护性：

```
┌─────────────────────────────────────┐
│           工具栏/面板区域              │
├─────────────────────────────────────┤
│  ┌─────────────────────────────────┐ │
│  │         画布容器                 │ │
│  │  ┌─────────────────────────────┐ │ │
│  │  │       渲染层                │ │ │
│  │  │  ┌─────────────────────────┐│ │ │
│  │  │  │      元素层             ││ │ │
│  │  │  └─────────────────────────┘│ │ │
│  │  │  ┌─────────────────────────┐│ │ │
│  │  │  │      选中框层           ││ │ │
│  │  │  └─────────────────────────┘│ │ │
│  │  │  ┌─────────────────────────┐│ │ │
│  │  │  │      辅助线层           ││ │ │
│  │  │  └─────────────────────────┘│ │ │
│  │  └─────────────────────────────┘ │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2.2 核心组件

| 组件名称 | 职责 | 层级 |
|---------|------|------|
| 元素层 | 渲染设计元素 | z-index: 1 |
| 选中框层 | 显示选中状态和控制点 | z-index: 10 |
| 辅助线层 | 显示对齐辅助线 | z-index: 5 |
| 工具层 | 显示临时工具和提示 | z-index: 15 |

## 3. 元素与选中框的联系机制

### 3.1 数据绑定关系

#### 元素模型结构
```javascript
class DesignElement {
  constructor(id, type, properties) {
    this.id = id;                    // 唯一标识符
    this.type = type;                // 元素类型
    this.position = { x: 0, y: 0 };  // 位置信息
    this.size = { width: 0, height: 0 }; // 尺寸信息
    this.rotation = 0;               // 旋转角度
    this.scale = { x: 1, y: 1 };     // 缩放比例
    this.properties = properties;     // 其他属性
  }
  
  getBounds() {
    return {
      x: this.position.x,
      y: this.position.y,
      width: this.size.width,
      height: this.size.height
    };
  }
  
  getTransformedBounds() {
    // 计算变换后的边界框
    const bounds = this.getBounds();
    // 应用旋转、缩放等变换
    return this.applyTransforms(bounds);
  }
}
```

#### 选中状态管理器
```javascript
class SelectionManager {
  constructor() {
    this.selectedElements = new Set();
    this.selectionBox = new SelectionBox();
    this.observers = [];
  }
  
  selectElement(elementId) {
    this.selectedElements.add(elementId);
    this.updateSelectionBox();
    this.notifyObservers('selectionChanged');
  }
  
  deselectElement(elementId) {
    this.selectedElements.delete(elementId);
    this.updateSelectionBox();
    this.notifyObservers('selectionChanged');
  }
  
  selectMultiple(elementIds) {
    this.selectedElements.clear();
    elementIds.forEach(id => this.selectedElements.add(id));
    this.updateSelectionBox();
    this.notifyObservers('selectionChanged');
  }
  
  updateSelectionBox() {
    if (this.selectedElements.size === 0) {
      this.selectionBox.hide();
      return;
    }
    
    const elements = Array.from(this.selectedElements)
      .map(id => this.getElement(id));
    const bounds = this.calculateCombinedBounds(elements);
    this.selectionBox.updatePosition(bounds);
    this.selectionBox.show();
  }
}
```

### 3.2 事件驱动机制

#### 观察者模式实现
```javascript
class EventEmitter {
  constructor() {
    this.events = {};
  }
  
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }
  
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
  
  off(event, callback) {
    if (this.events[event]) {
      this.events[event] = this.events[event]
        .filter(cb => cb !== callback);
    }
  }
}
```

#### 元素变化监听
```javascript
class ElementObserver {
  constructor(selectionManager) {
    this.selectionManager = selectionManager;
  }
  
  onElementPositionChanged(element) {
    if (this.selectionManager.isSelected(element.id)) {
      this.selectionManager.updateSelectionBox();
    }
  }
  
  onElementSizeChanged(element) {
    if (this.selectionManager.isSelected(element.id)) {
      this.selectionManager.updateSelectionBox();
    }
  }
  
  onElementTransformChanged(element) {
    if (this.selectionManager.isSelected(element.id)) {
      this.selectionManager.updateSelectionBox();
    }
  }
}
```

## 4. 元素与选中框的定位方案

### 4.1 坐标系统

#### 画布坐标系
- **原点**: 画布左上角为(0, 0)
- **单位**: 像素(px)
- **方向**: X轴向右为正，Y轴向下为正

#### 变换矩阵
```javascript
class Transform {
  constructor() {
    this.matrix = [1, 0, 0, 1, 0, 0]; // [a, b, c, d, e, f]
  }
  
  translate(x, y) {
    this.matrix[4] += x;
    this.matrix[5] += y;
    return this;
  }
  
  scale(sx, sy) {
    this.matrix[0] *= sx;
    this.matrix[1] *= sx;
    this.matrix[2] *= sy;
    this.matrix[3] *= sy;
    return this;
  }
  
  rotate(angle) {
    const cos = Math.cos(angle);
    const sin = Math.sin(angle);
    const [a, b, c, d] = this.matrix;
    
    this.matrix[0] = a * cos + c * sin;
    this.matrix[1] = b * cos + d * sin;
    this.matrix[2] = c * cos - a * sin;
    this.matrix[3] = d * cos - b * sin;
    return this;
  }
  
  transformPoint(x, y) {
    const [a, b, c, d, e, f] = this.matrix;
    return {
      x: a * x + c * y + e,
      y: b * x + d * y + f
    };
  }
}
```

### 4.2 位置计算算法

#### 单元素边界框计算
```javascript
function calculateElementBounds(element) {
  const { position, size, rotation, scale } = element;
  
  // 基础边界框
  const bounds = {
    x: position.x,
    y: position.y,
    width: size.width * scale.x,
    height: size.height * scale.y
  };
  
  // 应用旋转变换
  if (rotation !== 0) {
    return calculateRotatedBounds(bounds, rotation);
  }
  
  return bounds;
}
```

#### 多元素组合边界框计算
```javascript
function calculateCombinedBounds(elements) {
  if (elements.length === 0) return null;
  
  let minX = Infinity, minY = Infinity;
  let maxX = -Infinity, maxY = -Infinity;
  
  elements.forEach(element => {
    const bounds = calculateElementBounds(element);
    const corners = getCornerPoints(bounds);
    
    corners.forEach(corner => {
      minX = Math.min(minX, corner.x);
      minY = Math.min(minY, corner.y);
      maxX = Math.max(maxX, corner.x);
      maxY = Math.max(maxY, corner.y);
    });
  });
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
}
```

#### 旋转元素边界框计算
```javascript
function calculateRotatedBounds(bounds, rotation) {
  const centerX = bounds.x + bounds.width / 2;
  const centerY = bounds.y + bounds.height / 2;
  
  const corners = [
    { x: bounds.x, y: bounds.y },
    { x: bounds.x + bounds.width, y: bounds.y },
    { x: bounds.x + bounds.width, y: bounds.y + bounds.height },
    { x: bounds.x, y: bounds.y + bounds.height }
  ];
  
  const rotatedCorners = corners.map(corner => {
    return rotatePoint(corner, centerX, centerY, rotation);
  });
  
  const minX = Math.min(...rotatedCorners.map(p => p.x));
  const minY = Math.min(...rotatedCorners.map(p => p.y));
  const maxX = Math.max(...rotatedCorners.map(p => p.x));
  const maxY = Math.max(...rotatedCorners.map(p => p.y));
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
}
```

### 4.3 实时同步机制

#### 性能优化的更新策略
```javascript
class PositionSynchronizer {
  constructor() {
    this.updateQueue = new Set();
    this.isUpdating = false;
  }
  
  requestUpdate(elementId) {
    this.updateQueue.add(elementId);
    if (!this.isUpdating) {
      this.scheduleUpdate();
    }
  }
  
  scheduleUpdate() {
    this.isUpdating = true;
    requestAnimationFrame(() => {
      this.processUpdates();
      this.isUpdating = false;
      
      if (this.updateQueue.size > 0) {
        this.scheduleUpdate();
      }
    });
  }
  
  processUpdates() {
    const elementsToUpdate = Array.from(this.updateQueue);
    this.updateQueue.clear();
    
    // 批量更新选中框位置
    this.batchUpdateSelectionBox(elementsToUpdate);
  }
}
```

#### 防抖处理
```javascript
class DebouncedUpdater {
  constructor(delay = 16) {
    this.delay = delay;
    this.timeoutId = null;
    this.pendingUpdates = new Set();
  }
  
  update(elementId) {
    this.pendingUpdates.add(elementId);
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    
    this.timeoutId = setTimeout(() => {
      this.flush();
    }, this.delay);
  }
  
  flush() {
    const updates = Array.from(this.pendingUpdates);
    this.pendingUpdates.clear();
    this.timeoutId = null;
    
    // 执行批量更新
    this.processBatchUpdate(updates);
  }
}
```

## 5. 布局方案

### 5.1 CSS Grid布局

#### 主布局结构
```css
.designer-container {
  display: grid;
  grid-template-areas:
    "toolbar toolbar toolbar"
    "sidebar canvas properties"
    "footer footer footer";
  grid-template-columns: 300px 1fr 300px;
  grid-template-rows: 60px 1fr 40px;
  height: 100vh;
  width: 100vw;
}

.toolbar {
  grid-area: toolbar;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.sidebar {
  grid-area: sidebar;
  background: #fafafa;
  border-right: 1px solid #ddd;
  overflow-y: auto;
}

.canvas-container {
  grid-area: canvas;
  position: relative;
  overflow: auto;
  background: #ffffff;
}

.properties-panel {
  grid-area: properties;
  background: #fafafa;
  border-left: 1px solid #ddd;
  overflow-y: auto;
}

.footer {
  grid-area: footer;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
}
```

#### 响应式布局
```css
@media (max-width: 1200px) {
  .designer-container {
    grid-template-areas:
      "toolbar toolbar"
      "canvas canvas"
      "footer footer";
    grid-template-columns: 1fr;
  }

  .sidebar,
  .properties-panel {
    position: fixed;
    top: 60px;
    bottom: 40px;
    width: 300px;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open,
  .properties-panel.open {
    transform: translateX(0);
  }

  .properties-panel {
    right: 0;
    transform: translateX(100%);
  }
}
```

### 5.2 分层渲染

#### 层级定义
```css
.canvas-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.background-layer {
  z-index: 0;
  background: #ffffff;
  background-image:
    linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.element-layer {
  z-index: 1;
  pointer-events: auto;
}

.guide-layer {
  z-index: 5;
  pointer-events: none;
}

.selection-layer {
  z-index: 10;
  pointer-events: none;
}

.tool-layer {
  z-index: 15;
  pointer-events: auto;
}

.overlay-layer {
  z-index: 20;
  pointer-events: none;
}
```

#### 元素容器
```css
.design-element {
  position: absolute;
  cursor: move;
  user-select: none;
  transform-origin: center center;
}

.design-element:hover {
  outline: 1px solid #007acc;
  outline-offset: 1px;
}

.design-element.selected {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

.design-element.dragging {
  opacity: 0.8;
  z-index: 999;
}
```

### 5.3 虚拟化渲染

#### 视口裁剪实现
```javascript
class ViewportRenderer {
  constructor(canvas) {
    this.canvas = canvas;
    this.viewport = { x: 0, y: 0, width: 0, height: 0 };
    this.elements = new Map();
    this.visibleElements = new Set();
  }

  updateViewport() {
    const container = this.canvas.container;
    this.viewport = {
      x: container.scrollLeft,
      y: container.scrollTop,
      width: container.clientWidth,
      height: container.clientHeight
    };

    this.updateVisibleElements();
  }

  updateVisibleElements() {
    const newVisibleElements = new Set();

    this.elements.forEach((element, id) => {
      if (this.isElementVisible(element)) {
        newVisibleElements.add(id);
      }
    });

    // 隐藏不可见元素
    this.visibleElements.forEach(id => {
      if (!newVisibleElements.has(id)) {
        this.hideElement(id);
      }
    });

    // 显示新可见元素
    newVisibleElements.forEach(id => {
      if (!this.visibleElements.has(id)) {
        this.showElement(id);
      }
    });

    this.visibleElements = newVisibleElements;
  }

  isElementVisible(element) {
    const bounds = element.getBounds();
    return !(
      bounds.x + bounds.width < this.viewport.x ||
      bounds.x > this.viewport.x + this.viewport.width ||
      bounds.y + bounds.height < this.viewport.y ||
      bounds.y > this.viewport.y + this.viewport.height
    );
  }
}
```

#### LOD (Level of Detail) 系统
```javascript
class LODManager {
  constructor() {
    this.lodLevels = [
      { minZoom: 0, maxZoom: 0.25, detail: 'low' },
      { minZoom: 0.25, maxZoom: 1, detail: 'medium' },
      { minZoom: 1, maxZoom: Infinity, detail: 'high' }
    ];
  }

  getLODLevel(zoom) {
    return this.lodLevels.find(level =>
      zoom >= level.minZoom && zoom < level.maxZoom
    );
  }

  renderElement(element, zoom) {
    const lod = this.getLODLevel(zoom);

    switch (lod.detail) {
      case 'low':
        return this.renderLowDetail(element);
      case 'medium':
        return this.renderMediumDetail(element);
      case 'high':
        return this.renderHighDetail(element);
    }
  }

  renderLowDetail(element) {
    // 简化渲染，只显示基本形状
    return this.createSimplifiedElement(element);
  }

  renderMediumDetail(element) {
    // 中等细节，显示主要内容
    return this.createMediumElement(element);
  }

  renderHighDetail(element) {
    // 完整渲染，显示所有细节
    return this.createFullElement(element);
  }
}
```

## 6. 核心技术实现

### 6.1 选中框组件

#### 选中框类实现
```javascript
class SelectionBox {
  constructor(canvas) {
    this.canvas = canvas;
    this.element = this.createSelectionElement();
    this.handles = this.createResizeHandles();
    this.isVisible = false;
    this.bounds = null;
  }

  createSelectionElement() {
    const element = document.createElement('div');
    element.className = 'selection-box';
    element.style.cssText = `
      position: absolute;
      border: 2px solid #007acc;
      background: rgba(0, 122, 204, 0.1);
      pointer-events: none;
      display: none;
    `;
    return element;
  }

  createResizeHandles() {
    const positions = [
      'nw', 'n', 'ne',
      'w',       'e',
      'sw', 's', 'se'
    ];

    return positions.map(position => {
      const handle = document.createElement('div');
      handle.className = `resize-handle resize-handle-${position}`;
      handle.style.cssText = `
        position: absolute;
        width: 8px;
        height: 8px;
        background: #007acc;
        border: 1px solid #ffffff;
        cursor: ${this.getCursorForPosition(position)};
        pointer-events: auto;
      `;

      this.positionHandle(handle, position);
      this.element.appendChild(handle);

      return { element: handle, position };
    });
  }

  getCursorForPosition(position) {
    const cursors = {
      'nw': 'nw-resize', 'n': 'n-resize', 'ne': 'ne-resize',
      'w': 'w-resize',                    'e': 'e-resize',
      'sw': 'sw-resize', 's': 's-resize', 'se': 'se-resize'
    };
    return cursors[position];
  }

  positionHandle(handle, position) {
    const positions = {
      'nw': { top: '-4px', left: '-4px' },
      'n':  { top: '-4px', left: 'calc(50% - 4px)' },
      'ne': { top: '-4px', right: '-4px' },
      'w':  { top: 'calc(50% - 4px)', left: '-4px' },
      'e':  { top: 'calc(50% - 4px)', right: '-4px' },
      'sw': { bottom: '-4px', left: '-4px' },
      's':  { bottom: '-4px', left: 'calc(50% - 4px)' },
      'se': { bottom: '-4px', right: '-4px' }
    };

    Object.assign(handle.style, positions[position]);
  }

  updatePosition(bounds) {
    this.bounds = bounds;
    this.element.style.cssText += `
      left: ${bounds.x}px;
      top: ${bounds.y}px;
      width: ${bounds.width}px;
      height: ${bounds.height}px;
    `;
  }

  show() {
    if (!this.isVisible) {
      this.element.style.display = 'block';
      this.canvas.selectionLayer.appendChild(this.element);
      this.isVisible = true;
    }
  }

  hide() {
    if (this.isVisible) {
      this.element.style.display = 'none';
      if (this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }
      this.isVisible = false;
    }
  }
}
```

#### 多选选中框
```javascript
class MultiSelectionBox extends SelectionBox {
  constructor(canvas) {
    super(canvas);
    this.selectedElements = [];
  }

  updateForMultipleElements(elements) {
    this.selectedElements = elements;
    const combinedBounds = this.calculateCombinedBounds(elements);
    this.updatePosition(combinedBounds);

    // 多选时可能需要不同的控制点
    this.updateHandlesForMultiSelection();
  }

  calculateCombinedBounds(elements) {
    if (elements.length === 0) return null;

    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    elements.forEach(element => {
      const bounds = element.getTransformedBounds();
      minX = Math.min(minX, bounds.x);
      minY = Math.min(minY, bounds.y);
      maxX = Math.max(maxX, bounds.x + bounds.width);
      maxY = Math.max(maxY, bounds.y + bounds.height);
    });

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  updateHandlesForMultiSelection() {
    // 多选时可能只显示移动和整体缩放控制点
    this.handles.forEach(handle => {
      const { position } = handle;
      if (['nw', 'ne', 'sw', 'se'].includes(position)) {
        handle.element.style.display = 'block';
      } else {
        handle.element.style.display = 'none';
      }
    });
  }
}
```

### 6.2 拖拽系统

#### 拖拽管理器
```javascript
class DragManager {
  constructor(canvas) {
    this.canvas = canvas;
    this.isDragging = false;
    this.dragData = null;
    this.dragThreshold = 3; // 像素
  }

  startDrag(element, event) {
    this.dragData = {
      element,
      startPos: { x: event.clientX, y: event.clientY },
      elementStartPos: element.getPosition(),
      hasMoved: false
    };

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);

    // 防止文本选择
    document.body.style.userSelect = 'none';
  }

  onMouseMove = (event) => {
    if (!this.dragData) return;

    const deltaX = event.clientX - this.dragData.startPos.x;
    const deltaY = event.clientY - this.dragData.startPos.y;

    // 检查是否超过拖拽阈值
    if (!this.dragData.hasMoved) {
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      if (distance < this.dragThreshold) return;

      this.dragData.hasMoved = true;
      this.isDragging = true;
      this.onDragStart();
    }

    this.onDrag(deltaX, deltaY);
  }

  onMouseUp = (event) => {
    if (this.isDragging) {
      this.onDragEnd();
    } else {
      this.onClick(event);
    }

    this.cleanup();
  }

  onDragStart() {
    this.dragData.element.classList.add('dragging');
    this.canvas.classList.add('dragging');

    // 显示拖拽预览
    this.showDragPreview();
  }

  onDrag(deltaX, deltaY) {
    const newPos = {
      x: this.dragData.elementStartPos.x + deltaX,
      y: this.dragData.elementStartPos.y + deltaY
    };

    // 应用网格对齐
    if (this.canvas.snapToGrid) {
      newPos.x = this.snapToGrid(newPos.x);
      newPos.y = this.snapToGrid(newPos.y);
    }

    this.dragData.element.setPosition(newPos);
    this.canvas.selectionManager.updateSelectionBox();

    // 显示对齐辅助线
    this.updateAlignmentGuides(newPos);
  }

  onDragEnd() {
    this.dragData.element.classList.remove('dragging');
    this.canvas.classList.remove('dragging');

    // 隐藏拖拽预览和辅助线
    this.hideDragPreview();
    this.hideAlignmentGuides();

    // 触发位置变化事件
    this.canvas.emit('elementMoved', {
      element: this.dragData.element,
      oldPosition: this.dragData.elementStartPos,
      newPosition: this.dragData.element.getPosition()
    });
  }

  onClick(event) {
    // 处理点击选择
    this.canvas.selectionManager.selectElement(
      this.dragData.element.id
    );
  }

  cleanup() {
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
    document.body.style.userSelect = '';

    this.isDragging = false;
    this.dragData = null;
  }

  snapToGrid(value) {
    const gridSize = this.canvas.gridSize || 10;
    return Math.round(value / gridSize) * gridSize;
  }
}
```

#### 多元素拖拽
```javascript
class MultiDragManager extends DragManager {
  startMultiDrag(elements, event) {
    this.dragData = {
      elements,
      startPos: { x: event.clientX, y: event.clientY },
      elementStartPositions: elements.map(el => el.getPosition()),
      hasMoved: false
    };

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
    document.body.style.userSelect = 'none';
  }

  onDrag(deltaX, deltaY) {
    this.dragData.elements.forEach((element, index) => {
      const startPos = this.dragData.elementStartPositions[index];
      const newPos = {
        x: startPos.x + deltaX,
        y: startPos.y + deltaY
      };

      if (this.canvas.snapToGrid) {
        newPos.x = this.snapToGrid(newPos.x);
        newPos.y = this.snapToGrid(newPos.y);
      }

      element.setPosition(newPos);
    });

    this.canvas.selectionManager.updateSelectionBox();
  }
}
```

### 6.3 缩放和旋转

#### 缩放控制器
```javascript
class ResizeController {
  constructor(canvas) {
    this.canvas = canvas;
    this.isResizing = false;
    this.resizeData = null;
  }

  startResize(element, handle, event) {
    this.resizeData = {
      element,
      handle,
      startPos: { x: event.clientX, y: event.clientY },
      startBounds: element.getBounds(),
      aspectRatio: element.size.width / element.size.height
    };

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
    this.isResizing = true;
  }

  onMouseMove = (event) => {
    if (!this.isResizing) return;

    const deltaX = event.clientX - this.resizeData.startPos.x;
    const deltaY = event.clientY - this.resizeData.startPos.y;

    const newBounds = this.calculateNewBounds(
      this.resizeData.startBounds,
      this.resizeData.handle,
      deltaX,
      deltaY
    );

    this.resizeData.element.setBounds(newBounds);
    this.canvas.selectionManager.updateSelectionBox();
  }

  calculateNewBounds(startBounds, handle, deltaX, deltaY) {
    const bounds = { ...startBounds };

    switch (handle) {
      case 'nw':
        bounds.x += deltaX;
        bounds.y += deltaY;
        bounds.width -= deltaX;
        bounds.height -= deltaY;
        break;
      case 'n':
        bounds.y += deltaY;
        bounds.height -= deltaY;
        break;
      case 'ne':
        bounds.y += deltaY;
        bounds.width += deltaX;
        bounds.height -= deltaY;
        break;
      case 'e':
        bounds.width += deltaX;
        break;
      case 'se':
        bounds.width += deltaX;
        bounds.height += deltaY;
        break;
      case 's':
        bounds.height += deltaY;
        break;
      case 'sw':
        bounds.x += deltaX;
        bounds.width -= deltaX;
        bounds.height += deltaY;
        break;
      case 'w':
        bounds.x += deltaX;
        bounds.width -= deltaX;
        break;
    }

    // 保持最小尺寸
    bounds.width = Math.max(bounds.width, 10);
    bounds.height = Math.max(bounds.height, 10);

    return bounds;
  }

  onMouseUp = () => {
    this.cleanup();
  }

  cleanup() {
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
    this.isResizing = false;
    this.resizeData = null;
  }
}
```

#### 旋转控制器
```javascript
class RotationController {
  constructor(canvas) {
    this.canvas = canvas;
    this.isRotating = false;
    this.rotationData = null;
  }

  startRotation(element, event) {
    const bounds = element.getBounds();
    const centerX = bounds.x + bounds.width / 2;
    const centerY = bounds.y + bounds.height / 2;

    this.rotationData = {
      element,
      center: { x: centerX, y: centerY },
      startAngle: this.calculateAngle(
        event.clientX, event.clientY, centerX, centerY
      ),
      elementStartRotation: element.rotation
    };

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
    this.isRotating = true;
  }

  onMouseMove = (event) => {
    if (!this.isRotating) return;

    const currentAngle = this.calculateAngle(
      event.clientX,
      event.clientY,
      this.rotationData.center.x,
      this.rotationData.center.y
    );

    const deltaAngle = currentAngle - this.rotationData.startAngle;
    let newRotation = this.rotationData.elementStartRotation + deltaAngle;

    // 角度对齐（按住Shift键时）
    if (event.shiftKey) {
      newRotation = this.snapAngle(newRotation, 15);
    }

    this.rotationData.element.setRotation(newRotation);
    this.canvas.selectionManager.updateSelectionBox();
  }

  calculateAngle(x, y, centerX, centerY) {
    return Math.atan2(y - centerY, x - centerX);
  }

  snapAngle(angle, snapDegrees) {
    const snapRadians = (snapDegrees * Math.PI) / 180;
    return Math.round(angle / snapRadians) * snapRadians;
  }

  onMouseUp = () => {
    this.cleanup();
  }

  cleanup() {
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
    this.isRotating = false;
    this.rotationData = null;
  }
}
```

## 7. 性能优化策略

### 7.1 渲染优化

#### Canvas vs DOM 选择策略
```javascript
class RenderStrategy {
  static shouldUseCanvas(element) {
    // 复杂图形、大量元素或需要高性能动画时使用Canvas
    return (
      element.type === 'complex-shape' ||
      element.hasComplexEffects ||
      element.isAnimating ||
      this.getElementCount() > 1000
    );
  }

  static shouldUseSVG(element) {
    // 矢量图形、需要交互或可缩放时使用SVG
    return (
      element.type === 'vector' ||
      element.needsInteraction ||
      element.isScalable
    );
  }

  static shouldUseDOM(element) {
    // 简单元素、文本或需要复杂样式时使用DOM
    return (
      element.type === 'text' ||
      element.type === 'simple-shape' ||
      element.hasComplexStyles
    );
  }
}
```

#### GPU加速优化
```css
.design-element {
  /* 启用GPU加速 */
  will-change: transform;
  transform: translateZ(0);

  /* 优化动画性能 */
  transition: transform 0.1s ease-out;
}

.design-element.animating {
  /* 动画期间保持GPU层 */
  will-change: transform, opacity;
}

.design-element.static {
  /* 静态元素移除will-change */
  will-change: auto;
}
```

#### 批量DOM操作
```javascript
class BatchDOMUpdater {
  constructor() {
    this.updates = [];
    this.isScheduled = false;
  }

  addUpdate(element, properties) {
    this.updates.push({ element, properties });
    this.scheduleUpdate();
  }

  scheduleUpdate() {
    if (this.isScheduled) return;

    this.isScheduled = true;
    requestAnimationFrame(() => {
      this.flushUpdates();
      this.isScheduled = false;
    });
  }

  flushUpdates() {
    // 使用DocumentFragment减少重排重绘
    const fragment = document.createDocumentFragment();

    this.updates.forEach(({ element, properties }) => {
      Object.assign(element.style, properties);
    });

    this.updates.length = 0;
  }
}
```

### 7.2 内存管理

#### 对象池模式
```javascript
class ObjectPool {
  constructor(createFn, resetFn, maxSize = 100) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
    this.pool = [];
    this.activeObjects = new Set();
  }

  acquire() {
    let obj;

    if (this.pool.length > 0) {
      obj = this.pool.pop();
    } else {
      obj = this.createFn();
    }

    this.activeObjects.add(obj);
    return obj;
  }

  release(obj) {
    if (!this.activeObjects.has(obj)) return;

    this.activeObjects.delete(obj);
    this.resetFn(obj);

    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    }
  }

  clear() {
    this.pool.length = 0;
    this.activeObjects.clear();
  }
}

// 使用示例
const selectionBoxPool = new ObjectPool(
  () => new SelectionBox(),
  (box) => box.reset(),
  50
);
```

#### 事件委托
```javascript
class EventDelegator {
  constructor(container) {
    this.container = container;
    this.handlers = new Map();

    // 统一事件监听
    this.container.addEventListener('mousedown', this.handleMouseDown);
    this.container.addEventListener('mousemove', this.handleMouseMove);
    this.container.addEventListener('mouseup', this.handleMouseUp);
  }

  handleMouseDown = (event) => {
    const element = this.findDesignElement(event.target);
    if (element) {
      const handler = this.handlers.get('mousedown');
      if (handler) handler(element, event);
    }
  }

  handleMouseMove = (event) => {
    // 处理鼠标移动事件
  }

  handleMouseUp = (event) => {
    // 处理鼠标释放事件
  }

  findDesignElement(target) {
    while (target && target !== this.container) {
      if (target.classList.contains('design-element')) {
        return target;
      }
      target = target.parentElement;
    }
    return null;
  }

  on(event, handler) {
    this.handlers.set(event, handler);
  }

  off(event) {
    this.handlers.delete(event);
  }
}
```

#### 内存泄漏防护
```javascript
class MemoryManager {
  constructor() {
    this.observers = new WeakMap();
    this.timers = new Set();
    this.eventListeners = new Map();
  }

  addObserver(target, observer) {
    if (!this.observers.has(target)) {
      this.observers.set(target, new Set());
    }
    this.observers.get(target).add(observer);
  }

  removeObserver(target, observer) {
    const observers = this.observers.get(target);
    if (observers) {
      observers.delete(observer);
    }
  }

  addTimer(timerId) {
    this.timers.add(timerId);
  }

  clearTimer(timerId) {
    clearTimeout(timerId);
    clearInterval(timerId);
    this.timers.delete(timerId);
  }

  addEventListener(target, event, handler) {
    const key = `${target}_${event}`;
    this.eventListeners.set(key, { target, event, handler });
    target.addEventListener(event, handler);
  }

  removeEventListener(target, event) {
    const key = `${target}_${event}`;
    const listener = this.eventListeners.get(key);
    if (listener) {
      target.removeEventListener(event, listener.handler);
      this.eventListeners.delete(key);
    }
  }

  cleanup() {
    // 清理所有定时器
    this.timers.forEach(timerId => {
      clearTimeout(timerId);
      clearInterval(timerId);
    });
    this.timers.clear();

    // 清理所有事件监听器
    this.eventListeners.forEach(({ target, event, handler }) => {
      target.removeEventListener(event, handler);
    });
    this.eventListeners.clear();
  }
}
```

## 8. 交互体验优化

### 8.1 视觉反馈

#### 悬停效果
```css
.design-element {
  transition: all 0.2s ease;
}

.design-element:hover {
  outline: 1px solid #007acc;
  outline-offset: 1px;
  box-shadow: 0 2px 8px rgba(0, 122, 204, 0.2);
}

.design-element.selected {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

.design-element.selected:hover {
  outline-color: #005a9e;
}
```

#### 拖拽预览
```javascript
class DragPreview {
  constructor() {
    this.previewElement = null;
  }

  show(element, offset = { x: 0, y: 0 }) {
    this.previewElement = element.cloneNode(true);
    this.previewElement.classList.add('drag-preview');
    this.previewElement.style.cssText += `
      position: fixed;
      pointer-events: none;
      opacity: 0.7;
      z-index: 9999;
      transform: translate(${offset.x}px, ${offset.y}px);
    `;

    document.body.appendChild(this.previewElement);
  }

  update(x, y) {
    if (this.previewElement) {
      this.previewElement.style.transform =
        `translate(${x}px, ${y}px)`;
    }
  }

  hide() {
    if (this.previewElement) {
      document.body.removeChild(this.previewElement);
      this.previewElement = null;
    }
  }
}
```

#### 对齐辅助线
```javascript
class AlignmentGuides {
  constructor(canvas) {
    this.canvas = canvas;
    this.guides = {
      vertical: [],
      horizontal: []
    };
    this.tolerance = 5; // 对齐容差
  }

  showGuides(draggedElement) {
    this.clearGuides();

    const draggedBounds = draggedElement.getBounds();
    const otherElements = this.canvas.elements.filter(
      el => el !== draggedElement
    );

    otherElements.forEach(element => {
      const bounds = element.getBounds();
      this.checkAlignment(draggedBounds, bounds);
    });
  }

  checkAlignment(draggedBounds, targetBounds) {
    // 垂直对齐检查
    this.checkVerticalAlignment(draggedBounds, targetBounds);

    // 水平对齐检查
    this.checkHorizontalAlignment(draggedBounds, targetBounds);
  }

  checkVerticalAlignment(draggedBounds, targetBounds) {
    const draggedCenter = draggedBounds.x + draggedBounds.width / 2;
    const targetCenter = targetBounds.x + targetBounds.width / 2;

    if (Math.abs(draggedCenter - targetCenter) < this.tolerance) {
      this.addVerticalGuide(targetCenter);
    }

    if (Math.abs(draggedBounds.x - targetBounds.x) < this.tolerance) {
      this.addVerticalGuide(targetBounds.x);
    }

    if (Math.abs(
      draggedBounds.x + draggedBounds.width -
      targetBounds.x - targetBounds.width
    ) < this.tolerance) {
      this.addVerticalGuide(targetBounds.x + targetBounds.width);
    }
  }

  checkHorizontalAlignment(draggedBounds, targetBounds) {
    const draggedCenter = draggedBounds.y + draggedBounds.height / 2;
    const targetCenter = targetBounds.y + targetBounds.height / 2;

    if (Math.abs(draggedCenter - targetCenter) < this.tolerance) {
      this.addHorizontalGuide(targetCenter);
    }

    if (Math.abs(draggedBounds.y - targetBounds.y) < this.tolerance) {
      this.addHorizontalGuide(targetBounds.y);
    }

    if (Math.abs(
      draggedBounds.y + draggedBounds.height -
      targetBounds.y - targetBounds.height
    ) < this.tolerance) {
      this.addHorizontalGuide(targetBounds.y + targetBounds.height);
    }
  }

  addVerticalGuide(x) {
    const guide = document.createElement('div');
    guide.className = 'alignment-guide vertical';
    guide.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: 0;
      width: 1px;
      height: 100%;
      background: #ff6b6b;
      pointer-events: none;
      z-index: 1000;
    `;

    this.canvas.guideLayer.appendChild(guide);
    this.guides.vertical.push(guide);
  }

  addHorizontalGuide(y) {
    const guide = document.createElement('div');
    guide.className = 'alignment-guide horizontal';
    guide.style.cssText = `
      position: absolute;
      left: 0;
      top: ${y}px;
      width: 100%;
      height: 1px;
      background: #ff6b6b;
      pointer-events: none;
      z-index: 1000;
    `;

    this.canvas.guideLayer.appendChild(guide);
    this.guides.horizontal.push(guide);
  }

  clearGuides() {
    [...this.guides.vertical, ...this.guides.horizontal]
      .forEach(guide => {
        if (guide.parentNode) {
          guide.parentNode.removeChild(guide);
        }
      });

    this.guides.vertical = [];
    this.guides.horizontal = [];
  }
}
```

### 8.2 操作便利性

#### 多选功能
```javascript
class MultiSelection {
  constructor(canvas) {
    this.canvas = canvas;
    this.isSelecting = false;
    this.selectionRect = null;
    this.startPos = null;
  }

  startRectSelection(event) {
    this.startPos = {
      x: event.clientX,
      y: event.clientY
    };

    this.selectionRect = document.createElement('div');
    this.selectionRect.className = 'selection-rect';
    this.selectionRect.style.cssText = `
      position: fixed;
      border: 1px dashed #007acc;
      background: rgba(0, 122, 204, 0.1);
      pointer-events: none;
      z-index: 1000;
    `;

    document.body.appendChild(this.selectionRect);
    this.isSelecting = true;

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
  }

  onMouseMove = (event) => {
    if (!this.isSelecting) return;

    const currentPos = {
      x: event.clientX,
      y: event.clientY
    };

    const rect = {
      x: Math.min(this.startPos.x, currentPos.x),
      y: Math.min(this.startPos.y, currentPos.y),
      width: Math.abs(currentPos.x - this.startPos.x),
      height: Math.abs(currentPos.y - this.startPos.y)
    };

    this.updateSelectionRect(rect);
    this.highlightIntersectingElements(rect);
  }

  updateSelectionRect(rect) {
    this.selectionRect.style.cssText += `
      left: ${rect.x}px;
      top: ${rect.y}px;
      width: ${rect.width}px;
      height: ${rect.height}px;
    `;
  }

  highlightIntersectingElements(selectionRect) {
    this.canvas.elements.forEach(element => {
      const elementRect = element.getBoundingClientRect();

      if (this.rectsIntersect(selectionRect, elementRect)) {
        element.classList.add('selection-candidate');
      } else {
        element.classList.remove('selection-candidate');
      }
    });
  }

  rectsIntersect(rect1, rect2) {
    return !(
      rect1.x + rect1.width < rect2.x ||
      rect2.x + rect2.width < rect1.x ||
      rect1.y + rect1.height < rect2.y ||
      rect2.y + rect2.height < rect1.y
    );
  }

  onMouseUp = () => {
    if (this.isSelecting) {
      this.finishSelection();
    }
    this.cleanup();
  }

  finishSelection() {
    const selectedElements = this.canvas.elements.filter(
      element => element.classList.contains('selection-candidate')
    );

    this.canvas.selectionManager.selectMultiple(
      selectedElements.map(el => el.id)
    );

    // 清理候选状态
    this.canvas.elements.forEach(element => {
      element.classList.remove('selection-candidate');
    });
  }

  cleanup() {
    if (this.selectionRect) {
      document.body.removeChild(this.selectionRect);
      this.selectionRect = null;
    }

    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);

    this.isSelecting = false;
    this.startPos = null;
  }
}
```

#### 快捷键支持
```javascript
class KeyboardShortcuts {
  constructor(canvas) {
    this.canvas = canvas;
    this.shortcuts = new Map();
    this.setupDefaultShortcuts();

    document.addEventListener('keydown', this.onKeyDown);
    document.addEventListener('keyup', this.onKeyUp);
  }

  setupDefaultShortcuts() {
    this.addShortcut('ctrl+c', () => this.copy());
    this.addShortcut('ctrl+v', () => this.paste());
    this.addShortcut('ctrl+x', () => this.cut());
    this.addShortcut('delete', () => this.delete());
    this.addShortcut('ctrl+z', () => this.undo());
    this.addShortcut('ctrl+y', () => this.redo());
    this.addShortcut('ctrl+a', () => this.selectAll());
    this.addShortcut('escape', () => this.deselectAll());
  }

  addShortcut(combination, handler) {
    this.shortcuts.set(combination, handler);
  }

  onKeyDown = (event) => {
    const combination = this.getKeyCombination(event);
    const handler = this.shortcuts.get(combination);

    if (handler) {
      event.preventDefault();
      handler();
    }
  }

  onKeyUp = (event) => {
    // 处理按键释放事件
  }

  getKeyCombination(event) {
    const parts = [];

    if (event.ctrlKey) parts.push('ctrl');
    if (event.shiftKey) parts.push('shift');
    if (event.altKey) parts.push('alt');
    if (event.metaKey) parts.push('meta');

    parts.push(event.key.toLowerCase());

    return parts.join('+');
  }

  copy() {
    const selectedElements = this.canvas.selectionManager.getSelectedElements();
    if (selectedElements.length > 0) {
      this.canvas.clipboard.copy(selectedElements);
    }
  }

  paste() {
    this.canvas.clipboard.paste();
  }

  cut() {
    const selectedElements = this.canvas.selectionManager.getSelectedElements();
    if (selectedElements.length > 0) {
      this.canvas.clipboard.copy(selectedElements);
      this.canvas.deleteElements(selectedElements);
    }
  }

  delete() {
    const selectedElements = this.canvas.selectionManager.getSelectedElements();
    if (selectedElements.length > 0) {
      this.canvas.deleteElements(selectedElements);
    }
  }

  undo() {
    this.canvas.historyManager.undo();
  }

  redo() {
    this.canvas.historyManager.redo();
  }

  selectAll() {
    this.canvas.selectionManager.selectAll();
  }

  deselectAll() {
    this.canvas.selectionManager.deselectAll();
  }
}
```

## 9. 总结

设计器画布的实现是一个复杂的系统工程，需要考虑以下关键要素：

### 9.1 核心要点

1. **分层架构**: 清晰的层级划分确保功能模块的独立性
2. **数据绑定**: 响应式的数据绑定机制保证UI与数据的同步
3. **事件驱动**: 基于事件的通信机制提供良好的扩展性
4. **性能优化**: 多种优化策略确保流畅的用户体验
5. **交互体验**: 丰富的视觉反馈和便捷的操作方式

### 9.2 技术选型建议

- **小型项目**: DOM + CSS Transform
- **中型项目**: DOM + SVG 混合方案
- **大型项目**: Canvas + WebGL 高性能方案
- **复杂项目**: 分层渲染 + 虚拟化技术

### 9.3 扩展方向

1. **协作功能**: 多人实时编辑
2. **版本控制**: 历史记录和回滚
3. **插件系统**: 可扩展的功能架构
4. **移动端适配**: 触摸操作优化
5. **AI辅助**: 智能布局和设计建议

这种实现方案具有良好的可维护性、可扩展性和性能表现，能够满足现代设计器应用的需求。
```
