import React from 'react'
import * as Select from '@radix-ui/react-select'
import { ChevronDown, Move, RotateCw } from 'lucide-react'
import { useEditorStore } from '../../store/editorStore'

const PropertyGroup = ({ title, children }) => (
  <div className="mb-6">
    <h4 className="font-medium text-gray-900 mb-3">{title}</h4>
    <div className="space-y-3">
      {children}
    </div>
  </div>
)

const PropertyField = ({ label, children }) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
    </label>
    {children}
  </div>
)

const NumberInput = ({ value, onChange, label, min, max, step = 1, unit = 'px' }) => (
  <PropertyField label={label}>
    <div className="flex items-center space-x-2">
      <input
        type="number"
        value={parseInt(value) || 0}
        onChange={(e) => onChange(e.target.value + unit)}
        min={min}
        max={max}
        step={step}
        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      />
      <span className="text-sm text-gray-500">{unit}</span>
    </div>
  </PropertyField>
)

const SpacingControl = ({ value, onChange, label }) => {
  const parseSpacing = (spacing) => {
    if (!spacing) return { top: 0, right: 0, bottom: 0, left: 0 }
    const values = spacing.split(' ').map(v => parseInt(v) || 0)
    if (values.length === 1) return { top: values[0], right: values[0], bottom: values[0], left: values[0] }
    if (values.length === 2) return { top: values[0], right: values[1], bottom: values[0], left: values[1] }
    if (values.length === 4) return { top: values[0], right: values[1], bottom: values[2], left: values[3] }
    return { top: 0, right: 0, bottom: 0, left: 0 }
  }

  const formatSpacing = (spacing) => {
    return `${spacing.top}px ${spacing.right}px ${spacing.bottom}px ${spacing.left}px`
  }

  const spacing = parseSpacing(value)

  const updateSpacing = (side, newValue) => {
    const newSpacing = { ...spacing, [side]: parseInt(newValue) || 0 }
    onChange(formatSpacing(newSpacing))
  }

  return (
    <PropertyField label={label}>
      <div className="grid grid-cols-3 gap-2">
        <div></div>
        <input
          type="number"
          value={spacing.top}
          onChange={(e) => updateSpacing('top', e.target.value)}
          placeholder="上"
          className="px-2 py-1 text-sm border border-gray-300 rounded text-center"
        />
        <div></div>
        
        <input
          type="number"
          value={spacing.left}
          onChange={(e) => updateSpacing('left', e.target.value)}
          placeholder="左"
          className="px-2 py-1 text-sm border border-gray-300 rounded text-center"
        />
        <div className="flex items-center justify-center text-xs text-gray-500">
          {label}
        </div>
        <input
          type="number"
          value={spacing.right}
          onChange={(e) => updateSpacing('right', e.target.value)}
          placeholder="右"
          className="px-2 py-1 text-sm border border-gray-300 rounded text-center"
        />
        
        <div></div>
        <input
          type="number"
          value={spacing.bottom}
          onChange={(e) => updateSpacing('bottom', e.target.value)}
          placeholder="下"
          className="px-2 py-1 text-sm border border-gray-300 rounded text-center"
        />
        <div></div>
      </div>
    </PropertyField>
  )
}

const LayoutPanel = ({ element }) => {
  const { updateElement } = useEditorStore()

  const updatePosition = (positionUpdates) => {
    updateElement(element.id, {
      position: { ...element.position, ...positionUpdates }
    })
  }

  const updateSize = (sizeUpdates) => {
    updateElement(element.id, {
      size: { ...element.size, ...sizeUpdates }
    })
  }

  const updateStyle = (styleUpdates) => {
    updateElement(element.id, {
      style: { ...element.style, ...styleUpdates }
    })
  }

  const updateZIndex = (zIndex) => {
    updateElement(element.id, { zIndex: parseInt(zIndex) || 1 })
  }

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-6">
        {/* 位置设置 */}
        <PropertyGroup title="位置">
          <div className="grid grid-cols-2 gap-3">
            <NumberInput
              value={element.position?.x + 'px' || '0px'}
              onChange={(value) => updatePosition({ x: parseInt(value) || 0 })}
              label="X 坐标"
              unit="px"
            />
            <NumberInput
              value={element.position?.y + 'px' || '0px'}
              onChange={(value) => updatePosition({ y: parseInt(value) || 0 })}
              label="Y 坐标"
              unit="px"
            />
          </div>

          {/* 快速对齐按钮 */}
          <div className="grid grid-cols-3 gap-2 mt-3">
            <button
              onClick={() => updatePosition({ x: 0 })}
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              左对齐
            </button>
            <button
              onClick={() => updatePosition({ x: 600 })} // 假设画布中心
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              居中
            </button>
            <button
              onClick={() => updatePosition({ x: 1200 })} // 假设画布右侧
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              右对齐
            </button>
          </div>
        </PropertyGroup>

        {/* 尺寸设置 */}
        <PropertyGroup title="尺寸">
          <div className="grid grid-cols-2 gap-3">
            <PropertyField label="宽度">
              <input
                type="text"
                value={element.size?.width || ''}
                onChange={(e) => updateSize({ width: e.target.value || undefined })}
                placeholder="如 320px 或 50%（留空为自适应）"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            <PropertyField label="高度">
              <input
                type="text"
                value={element.size?.height || ''}
                onChange={(e) => updateSize({ height: e.target.value || undefined })}
                placeholder="如 200px（留空为自适应）"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
          </div>

          {/* 预设尺寸 */}
          <div className="grid grid-cols-2 gap-2 mt-3">
            <button
              onClick={() => updateSize({ width: '100%', height: 'auto' })}
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              全宽
            </button>
            <button
              onClick={() => updateSize({ width: 'auto', height: 'auto' })}
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              自适应
            </button>
          </div>
        </PropertyGroup>

        {/* 间距设置 */}
        <PropertyGroup title="间距">
          <SpacingControl
            value={element.style?.margin || '0px'}
            onChange={(value) => updateStyle({ margin: value })}
            label="外边距"
          />
          
          <SpacingControl
            value={element.style?.padding || '0px'}
            onChange={(value) => updateStyle({ padding: value })}
            label="内边距"
          />
        </PropertyGroup>

        {/* 层级设置 */}
        <PropertyGroup title="层级">
          <NumberInput
            value={element.zIndex + '' || '1'}
            onChange={(value) => updateZIndex(value)}
            label="Z-Index"
            min={1}
            max={1000}
            unit=""
          />

          {/* 层级快速调整 */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => updateZIndex((element.zIndex || 1) + 1)}
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              上移一层
            </button>
            <button
              onClick={() => updateZIndex(Math.max(1, (element.zIndex || 1) - 1))}
              className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              下移一层
            </button>
          </div>
        </PropertyGroup>

        {/* 显示设置 */}
        <PropertyGroup title="显示">
          <PropertyField label="显示状态">
            <Select.Root
              value={element.visible ? 'visible' : 'hidden'}
              onValueChange={(value) => updateElement(element.id, { visible: value === 'visible' })}
            >
              <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                <Select.Value />
                <ChevronDown className="w-4 h-4 text-gray-500" />
              </Select.Trigger>
              <Select.Portal>
                <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <Select.Viewport className="p-1">
                    <Select.Item value="visible" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                      <Select.ItemText>显示</Select.ItemText>
                    </Select.Item>
                    <Select.Item value="hidden" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                      <Select.ItemText>隐藏</Select.ItemText>
                    </Select.Item>
                  </Select.Viewport>
                </Select.Content>
              </Select.Portal>
            </Select.Root>
          </PropertyField>

          <PropertyField label="锁定状态">
            <Select.Root
              value={element.locked ? 'locked' : 'unlocked'}
              onValueChange={(value) => updateElement(element.id, { locked: value === 'locked' })}
            >
              <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                <Select.Value />
                <ChevronDown className="w-4 h-4 text-gray-500" />
              </Select.Trigger>
              <Select.Portal>
                <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <Select.Viewport className="p-1">
                    <Select.Item value="unlocked" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                      <Select.ItemText>解锁</Select.ItemText>
                    </Select.Item>
                    <Select.Item value="locked" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                      <Select.ItemText>锁定</Select.ItemText>
                    </Select.Item>
                  </Select.Viewport>
                </Select.Content>
              </Select.Portal>
            </Select.Root>
          </PropertyField>
        </PropertyGroup>

        {/* 变换设置 */}
        <PropertyGroup title="变换">
          <NumberInput
            value={element.style?.transform?.match(/rotate\((\d+)deg\)/)?.[1] || '0'}
            onChange={(value) => updateStyle({ 
              transform: `rotate(${value}deg)` 
            })}
            label="旋转角度"
            min={0}
            max={360}
            unit="°"
          />
        </PropertyGroup>
      </div>
    </div>
  )
}

export default LayoutPanel
