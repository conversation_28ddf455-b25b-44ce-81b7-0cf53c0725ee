import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Settings, Palette, Layout, Type } from 'lucide-react'
import * as Tabs from '@radix-ui/react-tabs'
import { useEditorStore } from '../store/editorStore'
import StylePanel from './properties/StylePanel'
import LayoutPanel from './properties/LayoutPanel'
import ContentPanel from './properties/ContentPanel'

const RightPanel = () => {
  const selectedElementData = useEditorStore(s => s.selectedElementData)

  if (!selectedElementData) {
    return (
      <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
        <div className="h-14 px-6 flex items-center border-b border-gray-200 bg-gray-50">
          <h2 className="font-semibold text-gray-900">属性设置</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <Settings className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div className="text-lg font-medium mb-2">未选中元素</div>
            <div className="text-sm">点击画布中的元素开始编辑</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {/* 面板标题 */}
      <div className="h-14 px-6 flex items-center justify-between border-b border-gray-200 bg-gray-50">
        <h2 className="font-semibold text-gray-900">属性设置</h2>
        <div className="text-sm text-gray-500">
          {selectedElementData.type}
        </div>
      </div>

      {/* 属性标签页 */}
      <Tabs.Root defaultValue="style" className="flex-1 flex flex-col">
        <Tabs.List className="flex border-b border-gray-200 bg-gray-50">
          <Tabs.Trigger
            value="style"
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 data-[state=active]:text-primary-600 data-[state=active]:border-b-2 data-[state=active]:border-primary-600 transition-colors"
          >
            <Palette className="w-4 h-4" />
            <span>样式</span>
          </Tabs.Trigger>
          <Tabs.Trigger
            value="layout"
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 data-[state=active]:text-primary-600 data-[state=active]:border-b-2 data-[state=active]:border-primary-600 transition-colors"
          >
            <Layout className="w-4 h-4" />
            <span>布局</span>
          </Tabs.Trigger>
          <Tabs.Trigger
            value="content"
            className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 data-[state=active]:text-primary-600 data-[state=active]:border-b-2 data-[state=active]:border-primary-600 transition-colors"
          >
            <Type className="w-4 h-4" />
            <span>内容</span>
          </Tabs.Trigger>
        </Tabs.List>

        {/* 标签页内容 */}
        <div className="flex-1 overflow-hidden">
          <AnimatePresence mode="wait">
            <Tabs.Content value="style" className="h-full">
              <motion.div
                key="style"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                <StylePanel element={selectedElementData} />
              </motion.div>
            </Tabs.Content>

            <Tabs.Content value="layout" className="h-full">
              <motion.div
                key="layout"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                <LayoutPanel element={selectedElementData} />
              </motion.div>
            </Tabs.Content>

            <Tabs.Content value="content" className="h-full">
              <motion.div
                key="content"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="h-full"
              >
                <ContentPanel element={selectedElementData} />
              </motion.div>
            </Tabs.Content>
          </AnimatePresence>
        </div>
      </Tabs.Root>
    </div>
  )
}

export default RightPanel
