import React from 'react'
import { useDrag } from 'react-dnd'
import { motion } from 'framer-motion'
import { 
  Type, 
  Image, 
  MousePointer, 
  Square, 
  FileText, 
  Play, 
  Grid3X3, 
  MapPin 
} from 'lucide-react'
import { ELEMENT_TYPES } from '../../store/editorStore'

const ElementItem = ({ type, icon: Icon, label, description }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'element',
    item: { elementType: type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  return (
    <motion.div
      ref={drag}
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      className={`
        p-4 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg cursor-grab
        transition-all duration-200 group
        ${isDragging ? 'opacity-50 rotate-2' : ''}
      `}
      data-dnd-dragging={isDragging ? true : undefined}
    >
      <div className="flex flex-col items-center text-center space-y-2">
        <div className="w-12 h-12 bg-primary-100 group-hover:bg-primary-200 rounded-lg flex items-center justify-center transition-colors">
          <Icon className="w-6 h-6 text-primary-600" />
        </div>
        <div>
          <div className="font-medium text-gray-900 text-sm">{label}</div>
          <div className="text-xs text-gray-500 mt-1">{description}</div>
        </div>
      </div>
    </motion.div>
  )
}

const ElementsPanel = () => {
  const elements = [
    {
      type: ELEMENT_TYPES.TEXT,
      icon: Type,
      label: '文本',
      description: '添加可编辑的文本内容',
    },
    {
      type: ELEMENT_TYPES.IMAGE,
      icon: Image,
      label: '图片',
      description: '插入图片或从媒体库选择',
    },
    {
      type: ELEMENT_TYPES.BUTTON,
      icon: MousePointer,
      label: '按钮',
      description: '可点击的交互按钮',
    },
    {
      type: ELEMENT_TYPES.CONTAINER,
      icon: Square,
      label: '容器',
      description: '用于布局的容器元素',
    },
    {
      type: ELEMENT_TYPES.FORM,
      icon: FileText,
      label: '表单',
      description: '收集用户输入的表单',
    },
    {
      type: ELEMENT_TYPES.VIDEO,
      icon: Play,
      label: '视频',
      description: '嵌入视频播放器',
    },
    {
      type: ELEMENT_TYPES.GALLERY,
      icon: Grid3X3,
      label: '画廊',
      description: '图片网格展示',
    },
    {
      type: ELEMENT_TYPES.MAP,
      icon: MapPin,
      label: '地图',
      description: '嵌入地图组件',
    },
  ]

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-4">
        <div className="text-sm text-gray-600 mb-4">
          拖拽下方组件到画布中开始创建您的页面
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          {elements.map((element) => (
            <ElementItem
              key={element.type}
              type={element.type}
              icon={element.icon}
              label={element.label}
              description={element.description}
            />
          ))}
        </div>

        {/* 使用提示 */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 使用提示</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 拖拽组件到画布中央区域</li>
            <li>• 点击元素可以选中并编辑属性</li>
            <li>• 使用右侧面板调整样式</li>
            <li>• 按 Delete 键删除选中元素</li>
          </ul>
        </div>

        {/* 快捷键提示 */}
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">⌨️ 快捷键</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>撤销</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+Z</kbd>
            </div>
            <div className="flex justify-between">
              <span>重做</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+Y</kbd>
            </div>
            <div className="flex justify-between">
              <span>保存</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+S</kbd>
            </div>
            <div className="flex justify-between">
              <span>删除</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Delete</kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ElementsPanel
