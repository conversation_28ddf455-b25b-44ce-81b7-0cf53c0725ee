import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Monitor,
  Tablet,
  Smartphone,
  ZoomIn,
  ZoomOut,
  Eye,
  Upload,
  Save,
  RotateCcw,
  RotateCw,
  ChevronDown,
} from 'lucide-react'
import * as Select from '@radix-ui/react-select'
import * as Tooltip from '@radix-ui/react-tooltip'
import { useEditorStore, BREAKPOINTS } from '../store/editorStore'

const TopToolbar = () => {
  const {
    currentBreakpoint,
    setCurrentBreakpoint,
    zoomLevel,
    setZoomLevel,
    pages,
    switchPage,
    currentPage,
    save,
    undo,
    redo,
    canUndo,
    canRedo,
    clearCurrentPage,
    isPreviewMode,
    setPreviewMode,
    addContainer,
    selectedElements,
    updateElement,
  } = useEditorStore()
  const handleClearElements = () => {
    // 清空当前页面 main 区段元素
    const { pages } = useEditorStore.getState()
    const active = pages.find(p => p.active)
    if (!active || !Array.isArray(active.sections)) return
    const updated = pages.map(p => {
      if (!p.active) return p
      const sections = p.sections.map(s => ({ ...s, elements: [] }))
      return { ...p, sections }
    })
    useEditorStore.setState({ pages: updated, selectedElement: null })
  }

  // 以全局 store 为准，不再使用本地 state

  const breakpoints = [
    { id: BREAKPOINTS.DESKTOP, icon: Monitor, label: '桌面', width: '1200px' },
    { id: BREAKPOINTS.TABLET, icon: Tablet, label: '平板', width: '768px' },
    { id: BREAKPOINTS.MOBILE, icon: Smartphone, label: '手机', width: '375px' },
  ]

  const handleZoomChange = (delta) => {
    setZoomLevel(zoomLevel + delta)
  }

  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
    // 这里可以实现预览逻辑
  }

  const handlePublish = () => {
    // 这里可以实现发布逻辑
    alert('页面发布成功！')
  }

  return (
    <Tooltip.Provider>
      <div className="h-16 bg-gray-900 text-white flex items-center justify-between px-6 border-b border-gray-700">
        {/* 左侧 */}
        <div className="flex items-center space-x-6">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-success-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">VE</span>
            </div>
            <span className="font-semibold text-lg">Visual Editor</span>
          </div>

          {/* 页面选择器 */}
          <Select.Root value={currentPage?.id} onValueChange={switchPage}>
            <Select.Trigger className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-700 px-3 py-2 rounded-md transition-colors">
              <Select.Value placeholder="选择页面" />
              <ChevronDown className="w-4 h-4" />
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white text-gray-900 rounded-md shadow-lg border p-1 z-50">
                <Select.Viewport>
                  {pages.map((page) => (
                    <Select.Item
                      key={page.id}
                      value={page.id}
                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none"
                    >
                      <Select.ItemText>{page.name}</Select.ItemText>
                    </Select.Item>
                  ))}
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>

          {/* 自动保存指示器 */}
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
            <span>自动保存已启用</span>
          </div>
        </div>

        {/* 中央 */}
        <div className="flex items-center space-x-4">
          {/* 历史操作 */}
          <div className="flex items-center space-x-1">
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <button
                  onClick={undo}
                  disabled={!canUndo}
                  className="p-2 hover:bg-gray-800 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                  撤销 (Ctrl+Z)
                  <Tooltip.Arrow className="fill-gray-800" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <button
                  onClick={redo}
                  disabled={!canRedo}
                  className="p-2 hover:bg-gray-800 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RotateCw className="w-4 h-4" />
                </button>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                  重做 (Ctrl+Y)
                  <Tooltip.Arrow className="fill-gray-800" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>
          </div>

          {/* 断点选择器 */}
          <div className="flex items-center space-x-1 bg-gray-800 rounded-md p-1">
            {breakpoints.map(({ id, icon: Icon, label, width }) => (
              <Tooltip.Root key={id}>
                <Tooltip.Trigger asChild>
                  <button
                    onClick={() => setCurrentBreakpoint(id)}
                    className={`p-2 rounded transition-colors ${
                      currentBreakpoint === id
                        ? 'bg-primary-600 text-white'
                        : 'hover:bg-gray-700 text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                  </button>
                </Tooltip.Trigger>
                <Tooltip.Portal>
                  <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                    {label} ({width})
                    <Tooltip.Arrow className="fill-gray-800" />
                  </Tooltip.Content>
                </Tooltip.Portal>
              </Tooltip.Root>
            ))}
          </div>

          {/* 缩放控制 */}
          <div className="flex items-center space-x-2 bg-gray-800 rounded-md px-3 py-2">
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <button
                  onClick={() => handleZoomChange(-10)}
                  disabled={zoomLevel <= 50}
                  className="p-1 hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
                >
                  <ZoomOut className="w-4 h-4" />
                </button>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                  缩小
                  <Tooltip.Arrow className="fill-gray-800" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            <span className="text-sm font-mono min-w-[3rem] text-center">
              {zoomLevel}%
            </span>

            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <button
                  onClick={() => handleZoomChange(10)}
                  disabled={zoomLevel >= 200}
                  className="p-1 hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
                >
                  <ZoomIn className="w-4 h-4" />
                </button>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                  放大
                  <Tooltip.Arrow className="fill-gray-800" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>
          </div>
        </div>

        {/* 右侧 */}
        <div className="flex items-center space-x-3">
          {/* 清空所有元素（保留区段结构） */}
          <button
            onClick={handleClearElements}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-md transition-colors"
          >
            <span className="text-sm">清空元素</span>
          </button>
          {/* 清空页面按钮 */}
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <button
                onClick={() => {
                  if (window.confirm('清空当前页面的所有元素和容器？')) clearCurrentPage()
                }}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-md transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span className="text-sm">清空</span>
              </button>
            </Tooltip.Trigger>
            <Tooltip.Portal>
              <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                清空当前页面
                <Tooltip.Arrow className="fill-gray-800" />
              </Tooltip.Content>
            </Tooltip.Portal>
          </Tooltip.Root>
          {/* 保存按钮 */}
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <button
                onClick={save}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-md transition-colors"
              >
                <Save className="w-4 h-4" />
                <span className="text-sm">保存</span>
              </button>
            </Tooltip.Trigger>
            <Tooltip.Portal>
              <Tooltip.Content className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                手动保存 (Ctrl+S)
                <Tooltip.Arrow className="fill-gray-800" />
              </Tooltip.Content>
            </Tooltip.Portal>
          </Tooltip.Root>

          {/* 预览按钮 */}
          <button
            onClick={() => setPreviewMode(!isPreviewMode)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              isPreviewMode
                ? 'bg-primary-600 hover:bg-primary-700'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            <Eye className="w-4 h-4" />
            <span className="text-sm">预览</span>
          </button>

          {/* 发布按钮 */}
          <motion.button
            onClick={handlePublish}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-2 px-4 py-2 bg-success-600 hover:bg-success-700 rounded-md transition-colors"
          >
            <Upload className="w-4 h-4" />
            <span className="text-sm font-medium">发布</span>
          </motion.button>
        </div>
      </div>
    </Tooltip.Provider>
  )
}

export default TopToolbar
