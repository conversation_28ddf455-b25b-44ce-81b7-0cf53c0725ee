{"name": "visual-editor-react", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "zustand": "^4.4.7", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-slider": "^1.1.2", "react-colorful": "^5.6.1", "react-hotkeys-hook": "^4.4.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}