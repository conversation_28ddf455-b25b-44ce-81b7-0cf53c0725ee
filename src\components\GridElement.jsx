import React from 'react'
import { motion } from 'framer-motion'
import { useDesignerStore, ELEMENT_TYPES } from '../store/designerStore'

const GridElement = ({ element, sectionId }) => {
  const { 
    selectedElement, 
    selectedElements,
    selectElement, 
    selectMultipleElements,
    hoveredElement,
    setHoveredElement,
    moveElement
  } = useDesignerStore()

  const isSelected = selectedElement === element.id
  const isMultiSelected = selectedElements.includes(element.id)
  const isHovered = hoveredElement === element.id

  const handleClick = (e) => {
    e.stopPropagation()
    
    if (e.ctrlKey || e.metaKey) {
      // Ctrl+点击多选
      const newSelection = selectedElements.includes(element.id)
        ? selectedElements.filter(id => id !== element.id)
        : [...selectedElements, element.id]
      selectMultipleElements(newSelection)
    } else {
      selectElement(element.id)
    }
  }

  const handleDoubleClick = (e) => {
    e.stopPropagation()
    // 双击进入编辑模式
    if (element.type === ELEMENT_TYPES.TEXT) {
      // TODO: 实现文本编辑
    }
  }

  const renderElementContent = () => {
    switch (element.type) {
      case ELEMENT_TYPES.TEXT:
        return (
          <div
            className="w-full h-full flex items-center justify-center p-4"
            style={{
              fontSize: element.style?.fontSize,
              fontWeight: element.style?.fontWeight,
              color: element.style?.color,
              textAlign: element.style?.textAlign || 'center'
            }}
          >
            {element.content}
          </div>
        )

      case ELEMENT_TYPES.BUTTON:
        return (
          <div className="w-full h-full flex items-center justify-center p-4">
            <button
              className="px-6 py-3 rounded-md font-medium transition-colors"
              style={{
                backgroundColor: element.style?.backgroundColor,
                color: element.style?.color,
                borderRadius: element.style?.borderRadius
              }}
              onClick={(e) => e.preventDefault()}
            >
              {element.content}
            </button>
          </div>
        )

      case ELEMENT_TYPES.IMAGE:
        return (
          <div className="w-full h-full flex items-center justify-center p-4">
            <img
              src={element.src}
              alt={element.alt || '图片'}
              className="max-w-full max-h-full object-contain rounded"
              draggable={false}
            />
          </div>
        )

      case ELEMENT_TYPES.CONTAINER:
        return (
          <div
            className="w-full h-full flex items-center justify-center"
            style={{
              backgroundColor: element.style?.backgroundColor,
              border: element.style?.border,
              borderRadius: element.style?.borderRadius,
              padding: element.style?.padding
            }}
          >
            {element.children?.length > 0 ? (
              <div className="grid gap-2 w-full h-full">
                {/* 渲染子元素 */}
                {element.children.map((child) => (
                  <div key={child.id} className="border border-gray-200 rounded p-2">
                    {child.content || child.type}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500">
                <div className="text-2xl mb-2">📦</div>
                <div className="text-sm">容器</div>
                <div className="text-xs">拖拽元素到这里</div>
              </div>
            )}
          </div>
        )

      case ELEMENT_TYPES.STACK:
        return (
          <div
            className="w-full h-full relative"
            style={{
              backgroundColor: element.style?.backgroundColor,
              border: element.style?.border,
              borderRadius: element.style?.borderRadius
            }}
          >
            {/* 堆叠标识 */}
            <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium z-10">
              堆叠
            </div>
            
            {/* 堆叠内容 */}
            <div className="grid gap-1 w-full h-full p-4">
              {element.children?.map((child, index) => (
                <div
                  key={child.id}
                  className="border border-gray-200 rounded p-2 bg-white"
                  style={{
                    gridRow: child.gridPosition?.row,
                    gridColumn: child.gridPosition?.column,
                    gridRowEnd: `span ${child.gridPosition?.rowSpan || 1}`,
                    gridColumnEnd: `span ${child.gridPosition?.columnSpan || 1}`
                  }}
                >
                  {child.content || child.type}
                </div>
              ))}
            </div>
          </div>
        )

      default:
        return (
          <div className="w-full h-full flex items-center justify-center p-4 bg-gray-100 text-gray-600">
            未知组件: {element.type}
          </div>
        )
    }
  }

  return (
    <motion.div
      className={`
        relative cursor-pointer transition-all duration-200
        ${isSelected || isMultiSelected ? 'ring-2 ring-blue-500 ring-opacity-75' : ''}
        ${isHovered ? 'ring-1 ring-gray-400 ring-opacity-50' : ''}
      `}
      style={{
        gridRow: element.gridPosition.row,
        gridColumn: element.gridPosition.column,
        gridRowEnd: `span ${element.gridPosition.rowSpan}`,
        gridColumnEnd: `span ${element.gridPosition.columnSpan}`,
        backgroundColor: element.style?.backgroundColor,
        borderRadius: element.style?.borderRadius,
        minHeight: '80px'
      }}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setHoveredElement(element.id)}
      onMouseLeave={() => setHoveredElement(null)}
      whileHover={{ scale: 1.02 }}
      animate={{
        scale: isSelected ? 1.02 : 1,
      }}
      transition={{ duration: 0.1 }}
    >
      {/* 元素内容 */}
      {renderElementContent()}

      {/* 选中状态指示器 */}
      {isSelected && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute -top-8 left-0 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium z-20"
        >
          {element.type}
        </motion.div>
      )}

      {/* 多选状态指示器 */}
      {isMultiSelected && selectedElements.length > 1 && (
        <div className="absolute top-2 right-2 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold z-20">
          {selectedElements.indexOf(element.id) + 1}
        </div>
      )}
    </motion.div>
  )
}

export default GridElement
