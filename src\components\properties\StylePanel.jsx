import React from 'react'
import { HexColorPicker } from 'react-colorful'
import * as Slider from '@radix-ui/react-slider'
import * as Select from '@radix-ui/react-select'
import { ChevronDown } from 'lucide-react'
import { useEditorStore } from '../../store/editorStore'

const PropertyGroup = ({ title, children }) => (
  <div className="mb-6">
    <h4 className="font-medium text-gray-900 mb-3">{title}</h4>
    <div className="space-y-3">
      {children}
    </div>
  </div>
)

const PropertyField = ({ label, children }) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
    </label>
    {children}
  </div>
)

const ColorPicker = ({ value, onChange, label }) => {
  const [showPicker, setShowPicker] = React.useState(false)

  return (
    <PropertyField label={label}>
      <div className="relative">
        <button
          onClick={() => setShowPicker(!showPicker)}
          className="w-full flex items-center space-x-3 p-2 border border-gray-300 rounded-md hover:border-gray-400 transition-colors"
        >
          <div
            className="w-6 h-6 rounded border border-gray-300"
            style={{ backgroundColor: value }}
          ></div>
          <span className="flex-1 text-left text-sm">{value}</span>
        </button>
        
        {showPicker && (
          <div className="absolute top-full left-0 mt-2 p-3 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
            <HexColorPicker
              color={value}
              onChange={onChange}
              style={{ width: '200px', height: '120px' }}
            />
            <input
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="w-full mt-2 px-2 py-1 text-sm border border-gray-300 rounded"
            />
          </div>
        )}
      </div>
    </PropertyField>
  )
}

const NumberInput = ({ value, onChange, label, min, max, step = 1, unit = 'px' }) => (
  <PropertyField label={label}>
    <div className="flex items-center space-x-2">
      <input
        type="number"
        value={parseInt(value) || 0}
        onChange={(e) => onChange(e.target.value + unit)}
        min={min}
        max={max}
        step={step}
        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      />
      <span className="text-sm text-gray-500">{unit}</span>
    </div>
  </PropertyField>
)

const SliderInput = ({ value, onChange, label, min = 0, max = 100, step = 1 }) => (
  <PropertyField label={label}>
    <div className="space-y-2">
      <Slider.Root
        value={[parseInt(value) || 0]}
        onValueChange={([newValue]) => onChange(newValue)}
        min={min}
        max={max}
        step={step}
        className="relative flex items-center w-full h-5"
      >
        <Slider.Track className="bg-gray-200 relative grow rounded-full h-2">
          <Slider.Range className="absolute bg-primary-600 rounded-full h-full" />
        </Slider.Track>
        <Slider.Thumb className="block w-4 h-4 bg-white border-2 border-primary-600 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500" />
      </Slider.Root>
      <div className="text-sm text-gray-500 text-center">{value}</div>
    </div>
  </PropertyField>
)

const StylePanel = ({ element }) => {
  const { updateElement } = useEditorStore()

  const updateStyle = (styleUpdates) => {
    updateElement(element.id, {
      style: { ...element.style, ...styleUpdates }
    })
  }

  const fontFamilies = [
    { value: 'Inter, sans-serif', label: 'Inter' },
    { value: 'system-ui, sans-serif', label: '系统默认' },
    { value: '"Microsoft YaHei", sans-serif', label: '微软雅黑' },
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: '"Times New Roman", serif', label: 'Times New Roman' },
  ]

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-6">
        {/* 颜色设置 */}
        <PropertyGroup title="颜色">
          <ColorPicker
            value={element.style?.backgroundColor || '#ffffff'}
            onChange={(color) => updateStyle({ backgroundColor: color })}
            label="背景颜色"
          />
          
          {(element.type === 'text' || element.type === 'button') && (
            <ColorPicker
              value={element.style?.color || '#000000'}
              onChange={(color) => updateStyle({ color: color })}
              label="文字颜色"
            />
          )}

          {element.style?.border && (
            <ColorPicker
              value={element.style?.borderColor || '#000000'}
              onChange={(color) => updateStyle({ borderColor: color })}
              label="边框颜色"
            />
          )}
        </PropertyGroup>

        {/* 字体设置 */}
        {(element.type === 'text' || element.type === 'button') && (
          <PropertyGroup title="字体">
            <PropertyField label="字体族">
              <Select.Root
                value={element.style?.fontFamily || 'Inter, sans-serif'}
                onValueChange={(value) => updateStyle({ fontFamily: value })}
              >
                <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <Select.Value />
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      {fontFamilies.map((font) => (
                        <Select.Item
                          key={font.value}
                          value={font.value}
                          className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none"
                          style={{ fontFamily: font.value }}
                        >
                          <Select.ItemText>{font.label}</Select.ItemText>
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </PropertyField>

            <NumberInput
              value={element.style?.fontSize || '16px'}
              onChange={(value) => updateStyle({ fontSize: value })}
              label="字体大小"
              min={8}
              max={72}
              unit="px"
            />

            <PropertyField label="字体粗细">
              <Select.Root
                value={element.style?.fontWeight || 'normal'}
                onValueChange={(value) => updateStyle({ fontWeight: value })}
              >
                <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <Select.Value />
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      <Select.Item value="normal" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>正常</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="bold" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>粗体</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="lighter" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>细体</Select.ItemText>
                      </Select.Item>
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </PropertyField>
          </PropertyGroup>
        )}

        {/* 边框设置 */}
        <PropertyGroup title="边框">
          <NumberInput
            value={element.style?.borderWidth || '0px'}
            onChange={(value) => updateStyle({ 
              borderWidth: value,
              borderStyle: value === '0px' ? 'none' : 'solid'
            })}
            label="边框宽度"
            min={0}
            max={20}
            unit="px"
          />

          <NumberInput
            value={element.style?.borderRadius || '0px'}
            onChange={(value) => updateStyle({ borderRadius: value })}
            label="圆角半径"
            min={0}
            max={50}
            unit="px"
          />
        </PropertyGroup>

        {/* 阴影设置 */}
        <PropertyGroup title="阴影">
          <SliderInput
            value={element.style?.boxShadow ? '10' : '0'}
            onChange={(value) => {
              if (value === 0) {
                updateStyle({ boxShadow: 'none' })
              } else {
                updateStyle({ 
                  boxShadow: `0 ${value}px ${value * 2}px rgba(0, 0, 0, 0.1)` 
                })
              }
            }}
            label="阴影强度"
            min={0}
            max={20}
          />
        </PropertyGroup>

        {/* 透明度 */}
        <PropertyGroup title="透明度">
          <SliderInput
            value={Math.round((parseFloat(element.style?.opacity || 1) * 100))}
            onChange={(value) => updateStyle({ opacity: value / 100 })}
            label="不透明度"
            min={0}
            max={100}
          />
        </PropertyGroup>
      </div>
    </div>
  )
}

export default StylePanel
