@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
  }
}

@layer components {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* 自定义动画 */
@keyframes pulse-success {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-success {
  animation: pulse-success 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 拖拽时的样式 */
.dragging {
  @apply opacity-50;
}

.drag-over {
  @apply ring-2 ring-blue-400 ring-opacity-50;
}

/* 选中元素的样式 */
.element-selected {
  @apply ring-2 ring-blue-500 ring-opacity-75;
  position: relative;
}

.element-selected::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #3b82f6;
  border-radius: 4px;
  pointer-events: none;
  z-index: 10;
}

/* 悬停效果 */
.element-hover {
  @apply ring-1 ring-gray-300 ring-opacity-50;
}

/* 响应式断点指示器 */
.breakpoint-indicator {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 0 8px 8px 0;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.breakpoint-indicator.show {
  opacity: 1;
}

/* 工具提示样式 */
.tooltip {
  @apply bg-gray-900 text-white text-xs px-2 py-1 rounded shadow-lg;
  position: absolute;
  z-index: 1000;
  white-space: nowrap;
}

/* 加载动画 */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* 自定义滚动条 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(243 244 246);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* 面板切换动画 */
.panel-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.panel-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 200ms, transform 200ms;
}

.panel-exit {
  opacity: 1;
  transform: translateX(0);
}

.panel-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 200ms, transform 200ms;
}

/* 设计器画布样式 - 按照技术文档设计 */
.design-element {
  /* 移除transition避免拖拽时的延迟效果 */
  will-change: transform;
}

/* 移除悬停效果，选中框由SelectionOverlay统一管理 */
.design-element:hover {
  /* 悬停时不显示outline，由SelectionOverlay显示选中框预览 */
}

.design-element.selected {
  /* 选中状态也不显示outline，由SelectionOverlay显示选中框 */
}

.design-element.dragging {
  opacity: 0.8;
  z-index: 999 !important;
  will-change: transform, opacity;
  /* 拖拽时显示红色边框 */
  outline: 2px solid #ff6b6b !important;
  outline-offset: 0px;
  box-shadow: 0 0 8px rgba(255, 107, 107, 0.3) !important;
}

.design-element.static {
  will-change: auto;
}

/* 分层架构样式 */
[data-layer="background-layer"] {
  z-index: 0;
  pointer-events: none;
}

[data-layer="element-layer"] {
  z-index: 1;
  pointer-events: auto;
}

[data-layer="guide-layer"] {
  z-index: 5;
  pointer-events: none;
}

[data-layer="selection-layer"] {
  z-index: 10;
  pointer-events: none;
}

[data-layer="tool-layer"] {
  z-index: 15;
  pointer-events: none; /* 默认不接收事件，具体工具会单独设置 */
}

/* 对齐辅助线样式 */
.alignment-guide {
  position: absolute;
  background: #ff6b6b;
  pointer-events: none;
  z-index: 1000;
}

.alignment-guide.vertical {
  width: 1px;
  height: 100%;
}

.alignment-guide.horizontal {
  width: 100%;
  height: 1px;
}

/* 拖拽预览样式 */
.drag-preview {
  position: fixed;
  pointer-events: none;
  opacity: 0.7;
  z-index: 9999;
}

/* 多选框选样式 */
.selection-rect {
  position: fixed;
  border: 1px dashed #007acc;
  background: rgba(0, 122, 204, 0.1);
  pointer-events: none;
  z-index: 1000;
}

.selection-candidate {
  outline: 2px solid #ffa500 !important;
  outline-offset: 1px;
}
