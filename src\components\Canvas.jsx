import React, { useRef, useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useDrop } from 'react-dnd'
import { useEditorStore, BREAKPOINTS } from '../store/editorStore'
import SelectionOverlay from './SelectionOverlay'
import Section from './Section'
// 去除遮罩层，改为直接在元素上处理点击，选中框使用 SelectionOverlay 全局渲染

const Canvas = () => {
  const canvasRef = useRef(null)
  const {
    currentBreakpoint,
    zoomLevel,
    addSection,
    addElement,
    selectElement,
    pages, // 直接订阅pages状态
    beginMarquee,
    updateMarqueeRect,
    endMarquee,
    clearMarquee,
    marqueeActive,
    marqueeRect,
    setSelectedElements,
  } = useEditorStore()
  
  // 直接从pages计算currentSections，避免getter的缓存问题
  const currentPage = pages.find(p => p.active)
  const currentSections = currentPage?.sections || []
  
  // 调试日志已移除

  // 计算画布尺寸
  const getCanvasSize = () => {
    const width = currentBreakpoint === BREAKPOINTS.DESKTOP ? 1280 :
                  currentBreakpoint === BREAKPOINTS.TABLET ? 768 : 375
    const height = currentSections.reduce((total, section) => {
      return total + (parseInt(section.height) || 400)
    }, 0) || 800

    return { width, height }
  }

  const canvasSize = getCanvasSize()
  const scale = zoomLevel / 100

  // 兜底：在画布根节点处理 drop（当区段未触发 drop 时）
  const computeMargin = (parentRect, clientOffset) => {
    const x = clientOffset.x - parentRect.left
    const y = clientOffset.y - parentRect.top
    const centerX = parentRect.width / 2
    const centerY = parentRect.height / 2
    const useLeft = x <= centerX
    const useTop = y <= centerY
    return {
      top: useTop ? Math.max(0, Math.round(y)) : 0,
      right: !useLeft ? Math.max(0, Math.round(parentRect.width - x)) : 0,
      bottom: !useTop ? Math.max(0, Math.round(parentRect.height - y)) : 0,
      left: useLeft ? Math.max(0, Math.round(x)) : 0,
    }
  }

  const [{ isOverRoot }, dropRoot] = useDrop(() => ({
    accept: ['element', 'media'],
    drop: (item, monitor) => {
      if (monitor.didDrop()) return
      const clientOffset = monitor.getClientOffset()
      if (!clientOffset) {
        console.warn('Root drop: missing clientOffset')
        return
      }
      // 根据指针位置定位最近的区段（使用 pageX/Y 更稳健）
      const nodeAtPoint = document.elementFromPoint(
        Math.round(clientOffset.x),
        Math.round(clientOffset.y)
      )
      const sectionNode = nodeAtPoint?.closest?.('[data-section-id]')
      if (!sectionNode) {
        console.warn('Root drop: no section found at point', clientOffset, nodeAtPoint)
        return
      }
      const sectionId = sectionNode.getAttribute('data-section-id')
      const rect = sectionNode.getBoundingClientRect()
      const margin = computeMargin(rect, clientOffset)
      const elementType = item.elementType
        ? item.elementType
        : (item.mediaType === 'video' ? 'video' : 'image')
      console.log('Root drop addElement:', { sectionId, margin, elementType })
      addElement(elementType, margin, { sectionId })
    },
    collect: (monitor) => ({ isOverRoot: monitor.isOver({ shallow: true }) }),
  }))

  // 点击画布空白区域取消选择
  const handleCanvasClick = (e) => {
    if (skipNextCanvasClickRef.current) return
    if (e.target === e.currentTarget) {
      selectElement(null)
    }
  }

  // 框选手势：在画布舞台内监听
  const [marqueeStart, setMarqueeStart] = useState(null)
  const skipNextCanvasClickRef = useRef(false)
  const prevUserSelectRef = useRef('')
  const marqueeContainRef = useRef(false) // Alt 按住时使用“完全包含”模式
  const handleStageMouseDown = (e) => {
    e.preventDefault()
    // 只在点击空白区域时触发框选
    if (e.target.closest('[data-element-id]')) return
    const host = document.getElementById('selection-overlay-host')
    if (!host) return
    skipNextCanvasClickRef.current = true
    const hostRect = host.getBoundingClientRect()
    // 禁止文本选中
    prevUserSelectRef.current = document.body.style.userSelect
    document.body.style.userSelect = 'none'
    document.body.style.webkitUserSelect = 'none'
    const start = { x: e.clientX - hostRect.left, y: e.clientY - hostRect.top }
    setMarqueeStart(start)
    beginMarquee({ left: start.x, top: start.y, width: 0, height: 0 })
    // 取消已有选择
    selectElement(null)
    const onMove = (ev) => {
      marqueeContainRef.current = !!ev.altKey
      const cur = { x: ev.clientX - hostRect.left, y: ev.clientY - hostRect.top }
      const left = Math.min(start.x, cur.x)
      const top = Math.min(start.y, cur.y)
      const width = Math.abs(cur.x - start.x)
      const height = Math.abs(cur.y - start.y)
      updateMarqueeRect({ left, top, width, height })
    }
    const onUp = () => {
      endMarquee()
      window.removeEventListener('mousemove', onMove)
      window.removeEventListener('mouseup', onUp)
      // 恢复文本选中
      document.body.style.userSelect = prevUserSelectRef.current || ''
      document.body.style.webkitUserSelect = prevUserSelectRef.current || ''
      // 命中测试：与所有元素的外框重叠则选中
      const scope = canvasRef.current
      const elNodes = scope ? Array.from(scope.querySelectorAll('[data-element-id]')) : []
      const selected = []
      const r = marqueeRect || { left: 0, top: 0, width: 0, height: 0 }
      const rRight = r.left + r.width
      const rBottom = r.top + r.height
      const hostRect2 = host.getBoundingClientRect()
      elNodes.forEach(n => {
        const b = n.getBoundingClientRect()
        const left = b.left - hostRect2.left
        const top = b.top - hostRect2.top
        const right = left + b.width
        const bottom = top + b.height
        if (marqueeContainRef.current) {
          // 完全包含模式（Alt）
          const contain = left >= r.left && right <= rRight && top >= r.top && bottom <= rBottom
          if (contain) selected.push(n.getAttribute('data-element-id'))
        } else {
          // 相交模式（默认）
          const overlap = !(right < r.left || left > rRight || bottom < r.top || top > rBottom)
          if (overlap) selected.push(n.getAttribute('data-element-id'))
        }
      })
      setSelectedElements(selected)
      // 触发 SelectionOverlay 外接框计算
      requestAnimationFrame(() => {
        const evt = new Event('resize')
        window.dispatchEvent(evt)
      })
      clearMarquee()
      setMarqueeStart(null)
      // 避免 mouseup 触发的 click 清空选择
      setTimeout(() => { skipNextCanvasClickRef.current = false }, 0)
    }
    window.addEventListener('mousemove', onMove)
    window.addEventListener('mouseup', onUp)
  }

  return (
    <div className="flex-1 overflow-auto p-8" data-canvas-scroller style={{ background: '#f5f7fa' }}>
      <div
        className="flex justify-center"
        ref={(node) => {
          // 将兜底 drop 绑定在未缩放的外层容器上，避免 transform 影响坐标
          dropRoot(node)
        }}
      >
        <motion.div
          style={{
            width: canvasSize.width,
            height: canvasSize.height,
            transform: `scale(${scale})`,
            transformOrigin: 'top center',
          }}
          className="relative overflow-hidden"
          data-canvas-root={true}
          animate={{ scale }}
          transition={{ duration: 0.2 }}
          ref={(node) => {
            canvasRef.current = node
          }}
          onClick={handleCanvasClick}
          onMouseDown={handleStageMouseDown}
        >
          {/* 画布外框 */}
          <div className="absolute inset-0 pointer-events-none" style={{
            border: '1px solid rgba(0,0,0,0.08)',
            borderRadius: 4,
            boxShadow: '0 2px 12px rgba(0,0,0,0.06)'
          }} />

          {/* 区段布局 */}
          {currentSections.length === 0 ? (
            // 空状态
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="text-6xl mb-4">🎨</div>
                <div className="text-xl font-medium mb-2">开始创建您的页面</div>
                <div className="text-sm">从左侧拖拽组件到区段中</div>
              </div>
            </div>
          ) : (
            // 渲染区段
            <div className="flex flex-col h-full">
              {currentSections.map((section) => (
                <Section
                  key={section.id}
                  section={section}
                />
              ))}
            </div>
          )}
          {/* 框选遮罩层 */}
          <div className="absolute top-0 left-0 right-0 bottom-0 pointer-events-none">
            {marqueeActive && marqueeRect && (
              <div
                className="absolute"
                style={{
                  left: marqueeRect.left,
                  top: marqueeRect.top,
                  width: marqueeRect.width,
                  height: marqueeRect.height,
                  border: '1.5px solid #116dff',
                  background: 'rgba(17,109,255,0.06)'
                }}
              />
            )}
          </div>
        </motion.div>
      </div>

      {/* 画布信息 */}
      <div className="flex justify-center mt-4">
        <div className="bg-white px-4 py-2 rounded-lg shadow-sm border text-sm text-gray-600">
          <span className="font-medium">
            {canvasSize.width} × {canvasSize.height}
          </span>
          <span className="mx-2">•</span>
          <span>{zoomLevel}% 缩放</span>
          <span className="mx-2">•</span>
          <span>{currentSections.length} 个区段</span>
        </div>
      </div>

      {/* 渲染选中框（通过 portal 注入到画布内的 host）*/}
      <SelectionOverlay />

      {/* 断点指示器 */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-r-lg text-sm font-medium z-10"
      >
        {currentBreakpoint === BREAKPOINTS.DESKTOP && '🖥️ 桌面'}
        {currentBreakpoint === BREAKPOINTS.TABLET && '📱 平板'}
        {currentBreakpoint === BREAKPOINTS.MOBILE && '📱 手机'}
      </motion.div>
    </div>
  )
}

export default Canvas
