import React from 'react'
import * as Select from '@radix-ui/react-select'
import { ChevronDown, Plus, Trash2, Upload } from 'lucide-react'
import { useEditorStore, ELEMENT_TYPES } from '../../store/editorStore'

const PropertyGroup = ({ title, children }) => (
  <div className="mb-6">
    <h4 className="font-medium text-gray-900 mb-3">{title}</h4>
    <div className="space-y-3">
      {children}
    </div>
  </div>
)

const PropertyField = ({ label, children }) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
    </label>
    {children}
  </div>
)

const ContentPanel = ({ element }) => {
  const { updateElement } = useEditorStore()

  const updateContent = (updates) => {
    updateElement(element.id, updates)
  }

  const renderContentFields = () => {
    switch (element.type) {
      case ELEMENT_TYPES.TEXT:
        return (
          <PropertyGroup title="文本内容">
            <PropertyField label="文本内容">
              <textarea
                value={element.content || ''}
                onChange={(e) => updateContent({ content: e.target.value })}
                placeholder="输入文本内容..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
              />
            </PropertyField>
            
            <PropertyField label="文本对齐">
              <Select.Root
                value={element.style?.textAlign || 'left'}
                onValueChange={(value) => updateContent({ 
                  style: { ...element.style, textAlign: value }
                })}
              >
                <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <Select.Value />
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      <Select.Item value="left" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>左对齐</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="center" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>居中</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="right" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>右对齐</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="justify" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>两端对齐</Select.ItemText>
                      </Select.Item>
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </PropertyField>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.IMAGE:
        return (
          <PropertyGroup title="图片设置">
            <PropertyField label="图片地址">
              <input
                type="url"
                value={element.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                placeholder="https://example.com/image.jpg"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            
            <PropertyField label="替代文本">
              <input
                type="text"
                value={element.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                placeholder="图片描述"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>

            <PropertyField label="适应方式">
              <Select.Root
                value={element.style?.objectFit || 'cover'}
                onValueChange={(value) => updateContent({ 
                  style: { ...element.style, objectFit: value }
                })}
              >
                <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <Select.Value />
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      <Select.Item value="cover" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>覆盖</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="contain" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>包含</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="fill" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>填充</Select.ItemText>
                      </Select.Item>
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </PropertyField>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.BUTTON:
        return (
          <PropertyGroup title="按钮设置">
            <PropertyField label="按钮文本">
              <input
                type="text"
                value={element.content || ''}
                onChange={(e) => updateContent({ content: e.target.value })}
                placeholder="按钮文本"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            
            <PropertyField label="链接地址">
              <input
                type="url"
                value={element.href || ''}
                onChange={(e) => updateContent({ href: e.target.value })}
                placeholder="https://example.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>

            <PropertyField label="打开方式">
              <Select.Root
                value={element.target || '_self'}
                onValueChange={(value) => updateContent({ target: value })}
              >
                <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <Select.Value />
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      <Select.Item value="_self" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>当前窗口</Select.ItemText>
                      </Select.Item>
                      <Select.Item value="_blank" className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none">
                        <Select.ItemText>新窗口</Select.ItemText>
                      </Select.Item>
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </PropertyField>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.VIDEO:
        return (
          <PropertyGroup title="视频设置">
            <PropertyField label="视频地址">
              <input
                type="url"
                value={element.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                placeholder="https://example.com/video.mp4"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="controls"
                checked={element.controls !== false}
                onChange={(e) => updateContent({ controls: e.target.checked })}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="controls" className="text-sm text-gray-700">显示控制栏</label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoplay"
                checked={element.autoplay || false}
                onChange={(e) => updateContent({ autoplay: e.target.checked })}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="autoplay" className="text-sm text-gray-700">自动播放</label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="loop"
                checked={element.loop || false}
                onChange={(e) => updateContent({ loop: e.target.checked })}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="loop" className="text-sm text-gray-700">循环播放</label>
            </div>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.FORM:
        return (
          <PropertyGroup title="表单设置">
            <PropertyField label="表单字段">
              <div className="space-y-2">
                {element.fields?.map((field, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <Select.Root
                      value={field.type}
                      onValueChange={(value) => {
                        const newFields = [...(element.fields || [])]
                        newFields[index] = { ...field, type: value }
                        updateContent({ fields: newFields })
                      }}
                    >
                      <Select.Trigger className="w-24 px-2 py-1 text-sm bg-white border border-gray-300 rounded">
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Portal>
                        <Select.Content className="bg-white border border-gray-200 rounded shadow-lg z-50">
                          <Select.Viewport className="p-1">
                            <Select.Item value="text" className="px-2 py-1 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none">
                              <Select.ItemText>文本</Select.ItemText>
                            </Select.Item>
                            <Select.Item value="email" className="px-2 py-1 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none">
                              <Select.ItemText>邮箱</Select.ItemText>
                            </Select.Item>
                            <Select.Item value="tel" className="px-2 py-1 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none">
                              <Select.ItemText>电话</Select.ItemText>
                            </Select.Item>
                            <Select.Item value="textarea" className="px-2 py-1 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none">
                              <Select.ItemText>多行文本</Select.ItemText>
                            </Select.Item>
                          </Select.Viewport>
                        </Select.Content>
                      </Select.Portal>
                    </Select.Root>
                    
                    <input
                      type="text"
                      value={field.placeholder}
                      onChange={(e) => {
                        const newFields = [...(element.fields || [])]
                        newFields[index] = { ...field, placeholder: e.target.value }
                        updateContent({ fields: newFields })
                      }}
                      placeholder="占位符文本"
                      className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                    />
                    
                    <button
                      onClick={() => {
                        const newFields = element.fields?.filter((_, i) => i !== index) || []
                        updateContent({ fields: newFields })
                      }}
                      className="p-1 text-red-500 hover:bg-red-100 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
                
                <button
                  onClick={() => {
                    const newFields = [...(element.fields || []), { type: 'text', placeholder: '新字段', required: false }]
                    updateContent({ fields: newFields })
                  }}
                  className="w-full flex items-center justify-center space-x-2 p-2 border-2 border-dashed border-gray-300 hover:border-primary-400 rounded transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span className="text-sm">添加字段</span>
                </button>
              </div>
            </PropertyField>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.GALLERY:
        return (
          <PropertyGroup title="画廊设置">
            <PropertyField label="列数">
              <input
                type="number"
                value={element.columns || 3}
                onChange={(e) => updateContent({ columns: parseInt(e.target.value) || 3 })}
                min={1}
                max={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            
            <PropertyField label="间距">
              <input
                type="text"
                value={element.gap || '12px'}
                onChange={(e) => updateContent({ gap: e.target.value })}
                placeholder="12px"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
          </PropertyGroup>
        )

      case ELEMENT_TYPES.MAP:
        return (
          <PropertyGroup title="地图设置">
            <PropertyField label="位置">
              <input
                type="text"
                value={element.location || ''}
                onChange={(e) => updateContent({ location: e.target.value })}
                placeholder="北京市"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
            
            <PropertyField label="缩放级别">
              <input
                type="number"
                value={element.zoom || 10}
                onChange={(e) => updateContent({ zoom: parseInt(e.target.value) || 10 })}
                min={1}
                max={20}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </PropertyField>
          </PropertyGroup>
        )

      default:
        return (
          <div className="text-center text-gray-500 py-8">
            <div className="text-lg mb-2">🔧</div>
            <div>该元素类型暂无内容设置</div>
          </div>
        )
    }
  }

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      {renderContentFields()}
    </div>
  )
}

export default ContentPanel
