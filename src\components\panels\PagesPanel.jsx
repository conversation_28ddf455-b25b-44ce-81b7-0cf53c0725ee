import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Settings, Trash2, FileText } from 'lucide-react'
import * as Dialog from '@radix-ui/react-dialog'
import { useEditorStore } from '../../store/editorStore'

const PageItem = ({ page, isActive, onSelect, onDelete }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        group flex items-center justify-between p-4 rounded-lg cursor-pointer transition-all
        ${isActive 
          ? 'bg-primary-50 border border-primary-200' 
          : 'hover:bg-gray-50 border border-gray-200'
        }
      `}
      onClick={onSelect}
    >
      <div className="flex items-center space-x-3 flex-1">
        <div className={`
          w-10 h-10 rounded-lg flex items-center justify-center
          ${isActive ? 'bg-primary-100' : 'bg-gray-100'}
        `}>
          <FileText className={`w-5 h-5 ${isActive ? 'text-primary-600' : 'text-gray-600'}`} />
        </div>
        <div className="flex-1">
          <div className="font-medium text-gray-900">{page.name}</div>
          <div className="text-sm text-gray-500">
            {page.elements.length} 个元素
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={(e) => {
            e.stopPropagation()
            // 这里可以添加页面设置功能
          }}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="页面设置"
        >
          <Settings className="w-4 h-4 text-gray-600" />
        </button>

        {/* 只有在有多个页面时才显示删除按钮 */}
        {page.id !== 'home' && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="p-2 hover:bg-red-100 rounded transition-colors"
            title="删除页面"
          >
            <Trash2 className="w-4 h-4 text-red-500" />
          </button>
        )}
      </div>
    </motion.div>
  )
}

const AddPageDialog = ({ open, onOpenChange, onAddPage }) => {
  const [pageName, setPageName] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (pageName.trim()) {
      onAddPage(pageName.trim())
      setPageName('')
      onOpenChange(false)
    }
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-6 w-96 z-50">
          <Dialog.Title className="text-lg font-semibold text-gray-900 mb-4">
            添加新页面
          </Dialog.Title>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                页面名称
              </label>
              <input
                type="text"
                value={pageName}
                onChange={(e) => setPageName(e.target.value)}
                placeholder="输入页面名称"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                autoFocus
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <Dialog.Close asChild>
                <button
                  type="button"
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  取消
                </button>
              </Dialog.Close>
              <button
                type="submit"
                disabled={!pageName.trim()}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                添加页面
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

const PagesPanel = () => {
  const { pages, switchPage, addPage, deletePage } = useEditorStore()
  const [showAddDialog, setShowAddDialog] = useState(false)

  const handleAddPage = (name) => {
    addPage(name)
  }

  const handleDeletePage = (pageId) => {
    if (window.confirm('确定要删除这个页面吗？此操作无法撤销。')) {
      deletePage(pageId)
    }
  }

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-4">
        {/* 添加页面按钮 */}
        <button
          onClick={() => setShowAddDialog(true)}
          className="w-full flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-gray-300 hover:border-primary-400 rounded-lg transition-colors group"
        >
          <Plus className="w-5 h-5 text-gray-400 group-hover:text-primary-500" />
          <span className="text-gray-600 group-hover:text-primary-600 font-medium">
            添加新页面
          </span>
        </button>

        {/* 页面列表 */}
        <div className="space-y-3">
          {pages.map((page) => (
            <PageItem
              key={page.id}
              page={page}
              isActive={page.active}
              onSelect={() => switchPage(page.id)}
              onDelete={() => handleDeletePage(page.id)}
            />
          ))}
        </div>

        {/* 页面管理提示 */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 页面管理</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 点击页面名称切换到该页面</li>
            <li>• 每个页面都有独立的元素</li>
            <li>• 首页无法删除</li>
            <li>• 页面数据会自动保存</li>
          </ul>
        </div>

        {/* 页面统计 */}
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">📊 统计信息</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>总页面数</span>
              <span className="font-medium">{pages.length}</span>
            </div>
            <div className="flex justify-between">
              <span>当前页面元素</span>
              <span className="font-medium">
                {pages.find(p => p.active)?.elements.length || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span>总元素数</span>
              <span className="font-medium">
                {pages.reduce((total, page) => total + page.elements.length, 0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 添加页面对话框 */}
      <AddPageDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onAddPage={handleAddPage}
      />
    </div>
  )
}

export default PagesPanel
