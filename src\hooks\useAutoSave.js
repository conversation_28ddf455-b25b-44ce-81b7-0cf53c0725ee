import { useEffect } from 'react'
import { useEditorStore } from '../store/editorStore'

export const useAutoSave = (interval = 30000) => {
  const save = useEditorStore(state => state.save)
  const load = useEditorStore(state => state.load)

  // 初始化时加载数据
  useEffect(() => {
    load()
  }, [load])

  // 自动保存
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      save()
    }, interval)

    return () => clearInterval(autoSaveInterval)
  }, [save, interval])

  // 页面卸载时保存
  useEffect(() => {
    const handleBeforeUnload = () => {
      save()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [save])
}
