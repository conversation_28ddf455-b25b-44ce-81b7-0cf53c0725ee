import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

// 简单的 ID 生成函数
const generateId = () => Math.random().toString(36).substr(2, 9)

// 元素类型定义
export const ELEMENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  BUTTON: 'button',
  CONTAINER: 'container',
  FORM: 'form',
  VIDEO: 'video',
  GALLERY: 'gallery',
  MAP: 'map',
}

// 断点定义
export const BREAKPOINTS = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile',
}

// 面板类型
export const PANEL_TYPES = {
  ELEMENTS: 'elements',
  LAYERS: 'layers',
  PAGES: 'pages',
  THEME: 'theme',
  MEDIA: 'media',
}

// 创建默认元素
const createDefaultElement = (type, margin = { top: 100, left: 100, right: 0, bottom: 0 }) => {
  const baseElement = {
    id: generateId(),
    type,
    margin, // 使用 margin 进行定位，而不是 position
    size: { width: 'auto', height: 'auto' },
    style: {
      backgroundColor: 'transparent',
      color: '#333333',
      fontSize: '16px',
      fontFamily: 'Inter, sans-serif',
      padding: '8px',
      borderRadius: '4px',
      border: 'none',
    },
    visible: true,
    locked: false,
    zIndex: 1,
  }

  switch (type) {
    case ELEMENT_TYPES.TEXT:
      return {
        ...baseElement,
        content: '这是一段文本，点击编辑',
        style: { ...baseElement.style, padding: '16px' },
      }
    case ELEMENT_TYPES.IMAGE:
      return {
        ...baseElement,
        src: 'https://via.placeholder.com/300x200',
        alt: '图片',
        size: { width: '300px', height: '200px' },
      }
    case ELEMENT_TYPES.BUTTON:
      return {
        ...baseElement,
        content: '按钮',
        style: {
          ...baseElement.style,
          backgroundColor: '#22c55e',
          color: '#ffffff',
          padding: '12px 24px',
          borderRadius: '6px',
          cursor: 'pointer',
        },
      }
    case ELEMENT_TYPES.CONTAINER:
      return {
        ...baseElement,
        children: [],
        style: {
          ...baseElement.style,
          backgroundColor: '#f3f4f6',
          border: '2px dashed #d1d5db',
          padding: '32px',
          minHeight: '200px',
        },
      }
    case ELEMENT_TYPES.FORM:
      return {
        ...baseElement,
        fields: [
          { type: 'text', placeholder: '姓名', required: true },
          { type: 'email', placeholder: '邮箱', required: true },
        ],
        style: {
          ...baseElement.style,
          padding: '24px',
          border: '1px solid #d1d5db',
          borderRadius: '8px',
        },
      }
    case ELEMENT_TYPES.VIDEO:
      return {
        ...baseElement,
        src: '',
        controls: true,
        size: { width: '400px', height: '225px' },
      }
    case ELEMENT_TYPES.GALLERY:
      return {
        ...baseElement,
        images: [
          'https://via.placeholder.com/200x150',
          'https://via.placeholder.com/200x150/0000FF',
          'https://via.placeholder.com/200x150/FF0000',
        ],
        columns: 3,
        gap: '12px',
      }
    case ELEMENT_TYPES.MAP:
      return {
        ...baseElement,
        location: '北京市',
        zoom: 10,
        size: { width: '100%', height: '300px' },
      }
    default:
      return baseElement
  }
}

export const useEditorStore = create(
  subscribeWithSelector((set, get) => ({
    // 基本状态
    currentBreakpoint: BREAKPOINTS.DESKTOP,
    zoomLevel: 100,
    activePanel: PANEL_TYPES.ELEMENTS,
    
    // 页面和区段
    pages: [
      {
        id: 'home',
        name: '首页',
        sections: [
          {
            id: 'header',
            name: '头部区段',
            height: '120px',
            elements: [],
            containers: [],
            stacks: []
          },
          {
            id: 'main',
            name: '主要内容',
            height: '600px',
            elements: [],
            containers: [],
            stacks: []
          },
          {
            id: 'footer',
            name: '底部区段',
            height: '120px',
            elements: [],
            containers: [],
            stacks: []
          }
        ],
        active: true,
      }
    ],
    selectedSection: null,
    selectedElement: null,
    selectedElements: [],
    hoveredElement: null,
    // 框选（Marquee）
    marqueeActive: false,
    marqueeRect: null, // { left, top, width, height } 相对画布 host
    // 参考线（在 selection overlay 中渲染）
    guides: { v: [], h: [] },
    isDraggingElement: false,
    isResizingElement: false,
    showSelection: true, // 控制选中框的显示/隐藏
    
    // 历史记录
    history: [],
    historyIndex: -1,
    
    // 主题设置
    theme: {
      primaryColor: '#22c55e',
      fontFamily: 'Inter, sans-serif',
      preset: 'modern',
    },
    
    // 媒体库
    mediaLibrary: [
      {
        id: 'demo-1',
        name: '示例图片.jpg',
        src: 'https://via.placeholder.com/300x200',
        type: 'image',
      },
      {
        id: 'demo-2',
        name: '蓝色背景.jpg',
        src: 'https://via.placeholder.com/300x200/0000FF',
        type: 'image',
      },
    ],
    
    // Actions
    setCurrentBreakpoint: (breakpoint) => {
      set({ currentBreakpoint: breakpoint })
    },
    
    setZoomLevel: (level) => {
      set({ zoomLevel: Math.max(50, Math.min(200, level)) })
    },
    
    setActivePanel: (panel) => {
      set({ activePanel: panel })
    },
    
    // 容器操作
    addContainer: (sectionId, containerType = 'free', margin = { top: 100, left: 100 }) => {
      const container = {
        id: generateId(),
        type: containerType,
        name: `${containerType === 'stack' ? '堆叠' : '容器'} ${generateId().slice(-4)}`,
        margin,
        size: { width: '300px', height: '200px' },
        style: {
          backgroundColor: 'transparent',
          borderRadius: '4px',
          padding: '8px',
        },
        elements: [],
        // 布局相关属性
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        flexWrap: 'nowrap',
        gridCols: 3,
        gridRows: null,
        gap: '8px',
        showTitle: false,
      }

      const { pages } = get()
      const updatedPages = pages.map(page => {
        if (!page.active) return page

        const updatedSections = page.sections.map(section => {
          if (section.id === sectionId) {
            return {
              ...section,
              containers: [...(section.containers || []), container]
            }
          }
          return section
        })

        return { ...page, sections: updatedSections }
      })

      set({ pages: updatedPages })
      get().commitHistory()
    },

    updateContainer: (sectionId, containerId, updates) => {
      const { pages } = get()
      const updatedPages = pages.map(page => {
        if (!page.active) return page

        const updatedSections = page.sections.map(section => {
          if (section.id === sectionId) {
            const updatedContainers = section.containers?.map(container => {
              if (container.id === containerId) {
                return { ...container, ...updates }
              }
              return container
            }) || []

            return { ...section, containers: updatedContainers }
          }
          return section
        })

        return { ...page, sections: updatedSections }
      })

      set({ pages: updatedPages })
    },

    deleteContainer: (sectionId, containerId) => {
      const { pages } = get()
      const updatedPages = pages.map(page => {
        if (!page.active) return page

        const updatedSections = page.sections.map(section => {
          if (section.id === sectionId) {
            return {
              ...section,
              containers: section.containers?.filter(c => c.id !== containerId) || []
            }
          }
          return section
        })

        return { ...page, sections: updatedSections }
      })

      set({ pages: updatedPages })
      get().commitHistory()
    },

    // 元素操作
    addElement: (type, margin, options = {}) => {
      const element = createDefaultElement(type, margin)

      const { pages } = get()
      const activePage = pages.find(p => p.active)

      if (activePage && options.sectionId) {
        const updatedPages = pages.map(page => {
          if (!page.active) return page

          // 确保页面有 sections 数组
          if (!page.sections || !Array.isArray(page.sections)) {
            console.warn('Page missing sections, initializing default sections')
            page.sections = [
              {
                id: 'header',
                name: '头部区段',
                height: '120px',
                elements: [],
                containers: [],
                stacks: []
              },
              {
                id: 'main',
                name: '主要内容',
                height: '600px',
                elements: [],
                containers: [],
                stacks: []
              },
              {
                id: 'footer',
                name: '底部区段',
                height: '120px',
                elements: [],
                containers: [],
                stacks: []
              }
            ]
          }

          const updatedSections = page.sections.map(section => {
            if (section.id === options.sectionId) {
              return {
                ...section,
                elements: [...(section.elements || []), element]
              }
            }
            return section
          })

          return { ...page, sections: updatedSections }
        })

        set({
          pages: updatedPages,
          selectedElement: element.id,
        })

        // 调试日志已移除
        get().saveToHistory()
      }
    },
    
    selectElement: (elementId) => {
      set({ selectedElement: elementId, selectedElements: elementId ? [elementId] : [] })
    },

    // 多选
    toggleSelectElement: (elementId) => {
      const { selectedElements } = get()
      const exists = selectedElements.includes(elementId)
      const newList = exists
        ? selectedElements.filter(id => id !== elementId)
        : [...selectedElements, elementId]
      set({ selectedElements: newList, selectedElement: newList[newList.length - 1] || null })
    },
    clearSelection: () => set({ selectedElement: null, selectedElements: [] }),
    setSelectedElements: (ids) => set({ selectedElements: ids, selectedElement: ids[ids.length - 1] || null }),
    
    setHoveredElement: (elementId) => {
      set({ hoveredElement: elementId })
    },

    // 参考线
    setGuides: (guides) => set({ guides }),
    clearGuides: () => set({ guides: { v: [], h: [] } }),

    // 框选控制
    beginMarquee: (rect) => set({ marqueeActive: true, marqueeRect: rect || { left: 0, top: 0, width: 0, height: 0 } }),
    updateMarqueeRect: (rect) => set({ marqueeRect: rect }),
    endMarquee: () => set({ marqueeActive: false }),
    clearMarquee: () => set({ marqueeActive: false, marqueeRect: null }),

    // 区段操作
    selectSection: (sectionId) => {
      set({ selectedSection: sectionId })
    },

    addSection: (afterSectionId = null) => {
      const { pages } = get()
      const activePage = pages.find(p => p.active)

      if (activePage) {
        const newSection = {
          id: generateId(),
          name: '新区段',
          height: '400px',
          elements: [],
          containers: [],
          stacks: []
        }

        let updatedSections
        if (afterSectionId) {
          const index = activePage.sections.findIndex(s => s.id === afterSectionId)
          updatedSections = [
            ...activePage.sections.slice(0, index + 1),
            newSection,
            ...activePage.sections.slice(index + 1)
          ]
        } else {
          updatedSections = [...activePage.sections, newSection]
        }

        const updatedPages = pages.map(page =>
          page.active
            ? { ...page, sections: updatedSections }
            : page
        )

        set({ pages: updatedPages })
        get().saveToHistory()
      }
    },

    updateElement: (elementId, updates, options = { commitHistory: true }) => {
      const { pages } = get()

      // 在所有页面的 sections 中更新元素（支持区段 elements 与 containers，以及容器 children）
      const updatedPages = pages.map(page => {
        if (!Array.isArray(page.sections)) return page

        const updatedSections = page.sections.map(section => {
          // 更新区段 elements 中的元素
          const updatedElements = (section.elements || []).map(el =>
            el.id === elementId ? { ...el, ...updates } : el
          )

          // 更新区段 containers（若数据结构存在此数组）
          const updatedContainers = (section.containers || []).map(container =>
            container.id === elementId
              ? { ...container, ...updates }
              : container
          )

          // 如果是更新容器的 children，通常通过容器 id 定位，这里不递归 children 的单个元素更新
          // 如需更新 children 内的具体元素，后续可扩展一个专用 action

          return {
            ...section,
            elements: updatedElements,
            containers: updatedContainers,
          }
        })

        return { ...page, sections: updatedSections }
      })

      set({ pages: updatedPages })
      if (options.commitHistory) {
        get().saveToHistory()
      }
    },
    
    deleteElement: (elementId) => {
      const { pages, selectedElement } = get()

      // 在所有页面/区段中删除目标元素；同时从容器 children 中移除
      const updatedPages = pages.map(page => {
        if (!Array.isArray(page.sections)) return page

        const updatedSections = page.sections.map(section => {
          const filteredElements = (section.elements || []).filter(el => el.id !== elementId)

          const filteredContainers = (section.containers || []).map(container => {
            if (container.id === elementId) {
              // 直接删除该容器由上层 elements/containers 过滤处理，这里保持结构
              return container
            }
            const newChildren = Array.isArray(container.children)
              ? container.children.filter(child => child.id !== elementId)
              : container.children
            return { ...container, children: newChildren }
          }).filter(c => c.id !== elementId)

          return {
            ...section,
            elements: filteredElements,
            containers: filteredContainers,
          }
        })

        return { ...page, sections: updatedSections }
      })

      set({
        pages: updatedPages,
        selectedElement: selectedElement === elementId ? null : selectedElement,
      })

      get().saveToHistory()
    },
    
    deleteSelectedElement: () => {
      const { selectedElement } = get()
      if (selectedElement) {
        get().deleteElement(selectedElement)
      }
    },

    // UI 状态
    setIsDraggingElement: (flag) => set({ isDraggingElement: flag }),
    setIsResizingElement: (flag) => set({ isResizingElement: flag }),
    setShowSelection: (show) => set({ showSelection: show }),
    
    // 页面操作
    addPage: (name) => {
      const { pages } = get()
      const newPage = {
        id: generateId(),
        name,
        elements: [],
        active: false,
      }
      
      set({ pages: [...pages, newPage] })
    },
    
    switchPage: (pageId) => {
      const { pages } = get()
      const updatedPages = pages.map(page => ({
        ...page,
        active: page.id === pageId,
      }))
      
      set({ 
        pages: updatedPages,
        selectedElement: null,
      })
    },
    
    deletePage: (pageId) => {
      const { pages } = get()
      if (pages.length <= 1) return // 至少保留一个页面
      
      const updatedPages = pages.filter(page => page.id !== pageId)
      
      // 如果删除的是当前页面，切换到第一个页面
      const deletedPage = pages.find(page => page.id === pageId)
      if (deletedPage?.active && updatedPages.length > 0) {
        updatedPages[0].active = true
      }
      
      set({ 
        pages: updatedPages,
        selectedElement: null,
      })
    },
    
    // 主题操作
    updateTheme: (themeUpdates) => {
      const { theme } = get()
      set({ theme: { ...theme, ...themeUpdates } })
    },
    
    // 媒体库操作
    addMediaItem: (mediaItem) => {
      const { mediaLibrary } = get()
      set({ mediaLibrary: [...mediaLibrary, { ...mediaItem, id: generateId() }] })
    },
    
    // 历史记录操作
    saveToHistory: () => {
      const { pages, history, historyIndex } = get()
      const newState = JSON.stringify(pages)
      
      // 移除当前位置之后的历史记录
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newState)
      
      // 限制历史记录数量
      if (newHistory.length > 50) {
        newHistory.shift()
      } else {
        set({ historyIndex: historyIndex + 1 })
      }
      
      set({ history: newHistory })
    },
    
    undo: () => {
      const { history, historyIndex } = get()
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1
        const state = JSON.parse(history[newIndex])
        set({ 
          pages: state,
          historyIndex: newIndex,
          selectedElement: null,
        })
      }
    },
    
    redo: () => {
      const { history, historyIndex } = get()
      if (historyIndex < history.length - 1) {
        const newIndex = historyIndex + 1
        const state = JSON.parse(history[newIndex])
        set({ 
          pages: state,
          historyIndex: newIndex,
          selectedElement: null,
        })
      }
    },
    
    // 计算属性
    get canUndo() {
      return get().historyIndex > 0
    },
    
    get canRedo() {
      return get().historyIndex < get().history.length - 1
    },
    
    get currentPage() {
      return get().pages.find(page => page.active)
    },

    get currentSections() {
      const state = get()
      const page = state.pages.find(p => p.active)
      
      // 调试日志已移除
      
      if (!page) return []

      // 如果页面没有 sections，返回页面本身的 sections（应该从初始数据获取）
      if (!page.sections || !Array.isArray(page.sections) || page.sections.length === 0) {
        console.warn('Page missing sections, using fallback sections')
        return [
          {
            id: 'header',
            name: '头部区段',
            height: '120px',
            elements: [],
            containers: [],
            stacks: []
          },
          {
            id: 'main',
            name: '主要内容',
            height: '600px',
            elements: [],
            containers: [],
            stacks: []
          },
          {
            id: 'footer',
            name: '底部区段',
            height: '120px',
            elements: [],
            containers: [],
            stacks: []
          }
        ]
      }

      return page.sections
    },

    get currentElements() {
      const sections = get().currentSections
      const allElements = []

      sections.forEach(section => {
        if (section.elements) {
          section.elements.forEach(element => {
            allElements.push({
              ...element,
              sectionId: section.id,
              sectionName: section.name
            })
          })
        }
      })

      return allElements
    },

    get selectedElementData() {
      const { selectedElement } = get()
      const page = get().currentPage
      if (!page || !selectedElement) return null

      // 在所有区段中查找元素
      for (const section of page.sections) {
        const element = section.elements.find(el => el.id === selectedElement)
        if (element) return element

        // 在容器中查找
        for (const container of section.containers || []) {
          const containerElement = container.children?.find(el => el.id === selectedElement)
          if (containerElement) return containerElement
        }
      }
      return null
    },
    
    // 数据持久化
    save: () => {
      const state = get()
      const dataToSave = {
        pages: state.pages,
        theme: state.theme,
        mediaLibrary: state.mediaLibrary,
      }
      localStorage.setItem('visual-editor-data', JSON.stringify(dataToSave))
    },
    
    load: () => {
      try {
        const saved = localStorage.getItem('visual-editor-data')
        if (saved) {
          const data = JSON.parse(saved)
          const migratedPages = migratePages(data.pages, get().pages)
          set({
            pages: migratedPages,
            theme: data.theme || get().theme,
            mediaLibrary: data.mediaLibrary || get().mediaLibrary,
          })
        }
      } catch (error) {
        console.error('Failed to load saved data:', error)
      }
    },
  }))
)

// 迁移/修复本地存储的页面数据，确保至少有一个活动页面
function migratePages(maybePages, fallbackPages) {
  const defaultSections = [
    { id: 'header', name: '头部区段', height: '120px', elements: [], containers: [], stacks: [] },
    { id: 'main', name: '主要内容', height: '600px', elements: [], containers: [], stacks: [] },
    { id: 'footer', name: '底部区段', height: '120px', elements: [], containers: [], stacks: [] },
  ]

  const defaultPages = fallbackPages && Array.isArray(fallbackPages) && fallbackPages.length > 0
    ? fallbackPages
    : [{ id: 'home', name: '首页', sections: defaultSections, active: true }]

  if (!Array.isArray(maybePages) || maybePages.length === 0) {
    return defaultPages
  }

  // 迁移策略：
  // 1) 如已存在 sections 数组，则保留并为每个 section 补齐必需字段
  // 2) 如不存在 sections 但存在扁平 elements，则将其放入默认 main 区段
  // 3) 其余字段保留
  const pages = maybePages.map(p => {
    const id = p.id || Math.random().toString(36).substr(2, 9)
    const name = p.name || '页面'
    const active = !!p.active

    if (Array.isArray(p.sections) && p.sections.length > 0) {
      const normalizedSections = p.sections.map((s, idx) => ({
        id: s.id || Math.random().toString(36).substr(2, 9),
        name: s.name || (idx === 0 ? '头部区段' : idx === p.sections.length - 1 ? '底部区段' : '主要内容'),
        height: s.height || '400px',
        elements: Array.isArray(s.elements) ? s.elements : [],
        containers: Array.isArray(s.containers) ? s.containers : [],
        stacks: Array.isArray(s.stacks) ? s.stacks : [],
      }))
      return { id, name, sections: normalizedSections, active }
    }

    const flatElements = Array.isArray(p.elements) ? p.elements : []
    const sections = defaultSections.map(sec => ({ ...sec }))
    if (flatElements.length > 0) {
      // 将旧的扁平元素迁入 main 区段
      const mainIndex = sections.findIndex(s => s.id === 'main')
      const idx = mainIndex >= 0 ? mainIndex : 1
      sections[idx] = { ...sections[idx], elements: flatElements }
    }
    return { id, name, sections, active }
  })

  const anyActive = pages.some(p => p.active)
  if (!anyActive && pages.length > 0) {
    pages[0].active = true
  }
  return pages
}
