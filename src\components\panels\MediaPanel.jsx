import React, { useRef } from 'react'
import { useDrag } from 'react-dnd'
import { motion } from 'framer-motion'
import { Upload, Image, Video, Trash2, Download } from 'lucide-react'
import { useEditorStore } from '../../store/editorStore'

const MediaItem = ({ media, onDelete }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'media',
    item: { mediaType: media.type, mediaSrc: media.src, mediaName: media.name },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  const isVideo = media.type === 'video'

  return (
    <motion.div
      ref={drag}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`
        group relative bg-white border border-gray-200 rounded-lg overflow-hidden cursor-grab
        transition-all duration-200 hover:shadow-md
        ${isDragging ? 'opacity-50 rotate-2' : ''}
      `}
    >
      {/* 媒体预览 */}
      <div className="aspect-square bg-gray-100 flex items-center justify-center overflow-hidden">
        {isVideo ? (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <Video className="w-8 h-8 text-gray-500" />
          </div>
        ) : (
          <img
            src={media.src}
            alt={media.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.style.display = 'none'
              e.target.nextSibling.style.display = 'flex'
            }}
          />
        )}
        <div className="hidden w-full h-full bg-gray-200 items-center justify-center">
          <Image className="w-8 h-8 text-gray-500" />
        </div>
      </div>

      {/* 媒体信息 */}
      <div className="p-3">
        <div className="text-sm font-medium text-gray-900 truncate" title={media.name}>
          {media.name}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {media.type === 'video' ? '视频' : '图片'}
        </div>
      </div>

      {/* 悬停操作 */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="flex space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation()
              // 下载功能
              const link = document.createElement('a')
              link.href = media.src
              link.download = media.name
              link.click()
            }}
            className="p-1 bg-white/90 hover:bg-white rounded shadow-sm"
            title="下载"
          >
            <Download className="w-3 h-3 text-gray-600" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete(media.id)
            }}
            className="p-1 bg-white/90 hover:bg-white rounded shadow-sm"
            title="删除"
          >
            <Trash2 className="w-3 h-3 text-red-500" />
          </button>
        </div>
      </div>

      {/* 拖拽指示 */}
      <div className="absolute inset-0 bg-primary-500/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
        <div className="bg-white/90 px-2 py-1 rounded text-xs font-medium text-gray-700">
          拖拽到画布
        </div>
      </div>
    </motion.div>
  )
}

const MediaPanel = () => {
  const { mediaLibrary, addMediaItem } = useEditorStore()
  const fileInputRef = useRef(null)

  const handleFileUpload = (files) => {
    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const mediaItem = {
            name: file.name,
            src: e.target.result,
            type: file.type.startsWith('video/') ? 'video' : 'image',
            size: file.size,
            uploadDate: new Date().toISOString(),
          }
          addMediaItem(mediaItem)
        }
        reader.readAsDataURL(file)
      }
    })
  }

  const handleDrop = (e) => {
    e.preventDefault()
    const files = e.dataTransfer.files
    handleFileUpload(files)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
  }

  const handleDeleteMedia = (mediaId) => {
    if (window.confirm('确定要删除这个媒体文件吗？')) {
      // 这里应该调用删除媒体的 action
      console.log('Delete media:', mediaId)
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-6">
        {/* 上传区域 */}
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          className="border-2 border-dashed border-gray-300 hover:border-primary-400 rounded-lg p-8 text-center transition-colors cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-600 mb-2">
            <span className="font-medium">点击上传</span> 或拖拽文件到这里
          </div>
          <div className="text-sm text-gray-500">
            支持 JPG、PNG、GIF、MP4、WebM 格式
          </div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*"
            onChange={(e) => handleFileUpload(e.target.files)}
            className="hidden"
          />
        </div>

        {/* 媒体统计 */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>共 {mediaLibrary.length} 个文件</span>
          <span>
            总大小: {formatFileSize(
              mediaLibrary.reduce((total, item) => total + (item.size || 0), 0)
            )}
          </span>
        </div>

        {/* 媒体网格 */}
        <div className="grid grid-cols-2 gap-4">
          {mediaLibrary.length === 0 ? (
            <div className="col-span-2 text-center py-12">
              <div className="text-gray-400 mb-2">
                <Image className="w-12 h-12 mx-auto" />
              </div>
              <div className="text-gray-500 text-sm">
                暂无媒体文件
              </div>
              <div className="text-gray-400 text-xs mt-1">
                上传图片或视频开始使用
              </div>
            </div>
          ) : (
            mediaLibrary.map((media) => (
              <MediaItem
                key={media.id}
                media={media}
                onDelete={handleDeleteMedia}
              />
            ))
          )}
        </div>

        {/* 使用提示 */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 媒体使用</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 拖拽媒体文件到画布中使用</li>
            <li>• 支持多种图片和视频格式</li>
            <li>• 文件会保存在浏览器本地</li>
            <li>• 建议使用压缩后的文件</li>
          </ul>
        </div>

        {/* 文件格式支持 */}
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">📁 支持格式</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-700 mb-1">图片格式</div>
              <div className="text-gray-600 space-y-1">
                <div>• JPEG / JPG</div>
                <div>• PNG</div>
                <div>• GIF</div>
                <div>• WebP</div>
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-700 mb-1">视频格式</div>
              <div className="text-gray-600 space-y-1">
                <div>• MP4</div>
                <div>• WebM</div>
                <div>• OGV</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MediaPanel
