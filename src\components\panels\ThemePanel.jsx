import React from 'react'
import { motion } from 'framer-motion'
import { HexColorPicker } from 'react-colorful'
import * as Select from '@radix-ui/react-select'
import { ChevronDown, Palette, Type, Layout } from 'lucide-react'
import { useEditorStore } from '../../store/editorStore'

const ColorSwatch = ({ color, isSelected, onClick, label }) => {
  return (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`
        w-12 h-12 rounded-lg border-2 transition-all
        ${isSelected ? 'border-gray-900 shadow-lg' : 'border-gray-200 hover:border-gray-400'}
      `}
      style={{ backgroundColor: color }}
      title={label}
    >
      {isSelected && (
        <div className="w-full h-full rounded-md flex items-center justify-center">
          <div className="w-3 h-3 bg-white rounded-full shadow-sm"></div>
        </div>
      )}
    </motion.button>
  )
}

const ThemePreset = ({ preset, isSelected, onClick }) => {
  const presets = {
    modern: {
      name: '现代风格',
      description: '简洁现代的设计风格',
      colors: ['#3B82F6', '#10B981', '#F59E0B'],
      gradient: 'from-blue-500 to-green-500',
    },
    classic: {
      name: '经典风格',
      description: '传统优雅的设计风格',
      colors: ['#374151', '#6B7280', '#9CA3AF'],
      gradient: 'from-gray-600 to-gray-400',
    },
    minimal: {
      name: '极简风格',
      description: '简约纯净的设计风格',
      colors: ['#000000', '#FFFFFF', '#F3F4F6'],
      gradient: 'from-black to-gray-100',
    },
  }

  const presetData = presets[preset]

  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className={`
        w-full p-4 rounded-lg border-2 transition-all text-left
        ${isSelected 
          ? 'border-primary-500 bg-primary-50' 
          : 'border-gray-200 hover:border-gray-300 bg-white'
        }
      `}
    >
      <div className="flex items-center space-x-3">
        <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${presetData.gradient}`}></div>
        <div className="flex-1">
          <div className="font-medium text-gray-900">{presetData.name}</div>
          <div className="text-sm text-gray-500">{presetData.description}</div>
        </div>
      </div>
      <div className="flex space-x-1 mt-3">
        {presetData.colors.map((color, index) => (
          <div
            key={index}
            className="w-6 h-6 rounded border border-gray-200"
            style={{ backgroundColor: color }}
          ></div>
        ))}
      </div>
    </motion.button>
  )
}

const ThemePanel = () => {
  const { theme, updateTheme } = useEditorStore()

  const predefinedColors = [
    { color: '#3B82F6', label: '蓝色' },
    { color: '#10B981', label: '绿色' },
    { color: '#F59E0B', label: '橙色' },
    { color: '#EF4444', label: '红色' },
    { color: '#8B5CF6', label: '紫色' },
    { color: '#06B6D4', label: '青色' },
    { color: '#84CC16', label: '柠檬绿' },
    { color: '#F97316', label: '橙红色' },
  ]

  const fontOptions = [
    { value: 'Inter, sans-serif', label: 'Inter (推荐)' },
    { value: 'system-ui, sans-serif', label: '系统默认' },
    { value: '"Microsoft YaHei", sans-serif', label: '微软雅黑' },
    { value: '"PingFang SC", sans-serif', label: '苹方' },
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: '"Times New Roman", serif', label: 'Times New Roman' },
    { value: '"JetBrains Mono", monospace', label: 'JetBrains Mono' },
  ]

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-8">
        {/* 主题色彩 */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Palette className="w-5 h-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">主题色彩</h3>
          </div>
          
          {/* 预设颜色 */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-3">预设颜色</div>
            <div className="grid grid-cols-4 gap-3">
              {predefinedColors.map(({ color, label }) => (
                <ColorSwatch
                  key={color}
                  color={color}
                  label={label}
                  isSelected={theme.primaryColor === color}
                  onClick={() => updateTheme({ primaryColor: color })}
                />
              ))}
            </div>
          </div>

          {/* 自定义颜色选择器 */}
          <div>
            <div className="text-sm text-gray-600 mb-3">自定义颜色</div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <HexColorPicker
                color={theme.primaryColor}
                onChange={(color) => updateTheme({ primaryColor: color })}
                style={{ width: '100%', height: '150px' }}
              />
              <div className="mt-3 flex items-center space-x-2">
                <div
                  className="w-8 h-8 rounded border border-gray-300"
                  style={{ backgroundColor: theme.primaryColor }}
                ></div>
                <input
                  type="text"
                  value={theme.primaryColor}
                  onChange={(e) => updateTheme({ primaryColor: e.target.value })}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="#000000"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 字体设置 */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Type className="w-5 h-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">字体设置</h3>
          </div>
          
          <Select.Root 
            value={theme.fontFamily} 
            onValueChange={(value) => updateTheme({ fontFamily: value })}
          >
            <Select.Trigger className="w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
              <Select.Value />
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                <Select.Viewport className="p-1">
                  {fontOptions.map((font) => (
                    <Select.Item
                      key={font.value}
                      value={font.value}
                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer outline-none"
                      style={{ fontFamily: font.value }}
                    >
                      <Select.ItemText>{font.label}</Select.ItemText>
                    </Select.Item>
                  ))}
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>

        {/* 预设主题 */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Layout className="w-5 h-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">预设主题</h3>
          </div>
          
          <div className="space-y-3">
            {['modern', 'classic', 'minimal'].map((preset) => (
              <ThemePreset
                key={preset}
                preset={preset}
                isSelected={theme.preset === preset}
                onClick={() => updateTheme({ preset })}
              />
            ))}
          </div>
        </div>

        {/* 主题预览 */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-4">主题预览</h3>
          <div 
            className="p-4 rounded-lg border border-gray-200"
            style={{ 
              fontFamily: theme.fontFamily,
              borderColor: theme.primaryColor + '40'
            }}
          >
            <div className="space-y-3">
              <h4 className="text-lg font-semibold" style={{ color: theme.primaryColor }}>
                标题文本
              </h4>
              <p className="text-gray-700" style={{ fontFamily: theme.fontFamily }}>
                这是一段示例文本，展示当前主题的字体和颜色效果。
              </p>
              <button 
                className="px-4 py-2 text-white rounded-md"
                style={{ backgroundColor: theme.primaryColor }}
              >
                示例按钮
              </button>
            </div>
          </div>
        </div>

        {/* 使用提示 */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 主题提示</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 主题色会应用到按钮和强调元素</li>
            <li>• 字体设置会影响所有文本元素</li>
            <li>• 预设主题包含完整的设计方案</li>
            <li>• 更改会实时应用到页面</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default ThemePanel
