import React from 'react'

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🎨 可视化编辑器
        </h1>
        <p className="text-gray-600 mb-6">
          基于 React 18 + Vite 构建的现代化可视化页面编辑器
        </p>
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">✅ 项目启动成功！</h3>
            <p className="text-blue-800 text-sm">
              所有依赖已正确安装，Tailwind CSS 正常工作
            </p>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-900 mb-2">🚀 下一步</h3>
            <p className="text-green-800 text-sm">
              将 App.jsx 替换为完整版本开始使用编辑器
            </p>
          </div>
        </div>
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            技术栈: React 18.2 + Vite + Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  )
}

export default App
