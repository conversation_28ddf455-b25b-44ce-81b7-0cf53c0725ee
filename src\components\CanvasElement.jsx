import React, { useRef, useState } from 'react'
import { useDrop } from 'react-dnd'
import { useEditorStore, ELEMENT_TYPES } from '../store/editorStore'
import { calculateAlignmentGuides, calculateSnapPosition, getElementRect } from '../utils/alignmentHelper'

const CanvasElement = ({ element }) => {
  const { updateElement, selectElement, toggleSelectElement, selectedElements, setGuides, clearGuides } = useEditorStore()
  const containerDropRef = useRef(null)
  const wrapperRef = useRef(null)
  const [isDragging, setIsDragging] = useState(false)

  // 容器内接受子元素拖拽（仅对容器类型元素）
  const [{ isOver: isDropOver, canDrop: canDropInto }, dropInto] = useDrop(() => ({
    accept: ['element', 'media'],
    canDrop: () => element.type === ELEMENT_TYPES.CONTAINER,
    drop: (item, monitor) => {
      if (element.type !== ELEMENT_TYPES.CONTAINER) return
      const clientOffset = monitor.getClientOffset()
      const rect = containerDropRef.current?.getBoundingClientRect()
      if (!clientOffset || !rect) return
      const x = clientOffset.x - rect.left
      const y = clientOffset.y - rect.top

      // 创建新的子元素，使用 margin 定位
      const newChild = {
        id: Math.random().toString(36).slice(2, 9),
        type: item.elementType ? item.elementType : (item.mediaType === 'video' ? ELEMENT_TYPES.VIDEO : ELEMENT_TYPES.IMAGE),
        margin: { top: Math.round(y), left: Math.round(x), right: 0, bottom: 0 },
        size: { width: 'auto', height: 'auto' },
        style: { backgroundColor: 'transparent' },
        content: item.elementType === ELEMENT_TYPES.TEXT ? '新文本' : undefined,
        src: item.elementType === ELEMENT_TYPES.IMAGE ? 'https://via.placeholder.com/150' : undefined,
      }
      const children = [...(element.children || []), newChild]
      updateElement(element.id, { children })
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  }))

  // 拖拽相关状态
  const draggingRef = useRef(false)
  const startMouseRef = useRef({ x: 0, y: 0 })
  const startMarginRef = useRef({ top: 0, left: 0 })
  const multiStartMarginsRef = useRef({})
  const prevUserSelectRef = useRef('')

  const onDragMouseDown = (e) => {
    if (e.button !== 0) return
    e.stopPropagation()
    e.preventDefault()
    if (e.metaKey || e.ctrlKey) {
      toggleSelectElement(element.id)
    } else {
      // 若已是多选且当前元素在集合中，则保持多选不变
      if (!(selectedElements.length > 1 && selectedElements.includes(element.id))) {
        selectElement(element.id)
      }
    }
    startMouseRef.current = { x: e.clientX, y: e.clientY }
    const m = element.margin || { top: 0, left: 0 }
    startMarginRef.current = { top: m.top || 0, left: m.left || 0 }
    // 记录多选中每个元素的起始 margin，用于保持相对位移
    const ids = useEditorStore.getState().selectedElements
    const pages = useEditorStore.getState().pages
    const active = pages.find(p => p.active)
    const sectionElements = active?.sections?.flatMap(s => s.elements || []) || []
    const map = {}
    ids.forEach(id => {
      const el = id === element.id ? element : sectionElements.find(e => e.id === id)
      const em = el?.margin || { top: 0, left: 0 }
      map[id] = { top: em.top || 0, left: em.left || 0 }
    })
    multiStartMarginsRef.current = map
    draggingRef.current = true
    setIsDragging(true)
    useEditorStore.getState().setIsDraggingElement(true)

    // 拖动开始时隐藏选中框
    useEditorStore.getState().setShowSelection(false)

    // 全局禁止文本选中，提升拖拽体验
    prevUserSelectRef.current = document.body.style.userSelect
    document.body.style.userSelect = 'none'
    document.body.style.webkitUserSelect = 'none'

    // 直接处理移动，不使用节流，确保选中框能实时跟随
    const onMove = (ev) => {
      if (!draggingRef.current) return
        const dx = ev.clientX - startMouseRef.current.x
        const dy = ev.clientY - startMouseRef.current.y
        let nextTop = Math.max(0, Math.round(startMarginRef.current.top + dy))
        let nextLeft = Math.max(0, Math.round(startMarginRef.current.left + dx))

        // 使用新的对齐辅助工具计算吸附
        const store = useEditorStore.getState()
        const pages = store.pages
        const activePage = pages.find(p => p.active)
        const currentSection = activePage?.sections?.find(s =>
          s.elements?.some(el => el.id === element.id)
        )

        if (currentSection) {
          // 获取同级元素
          const siblings = currentSection.elements?.filter(el => el.id !== element.id) || []

          // 创建当前元素的临时矩形（使用新位置）
          const currentRect = {
            top: nextTop,
            left: nextLeft,
            width: element.size?.width ? parseInt(element.size.width) : (wrapperRef.current?.offsetWidth || 100),
            height: element.size?.height ? parseInt(element.size.height) : (wrapperRef.current?.offsetHeight || 40)
          }

          // 计算参考线
          const guides = calculateAlignmentGuides(
            { ...element, margin: { top: nextTop, left: nextLeft } },
            siblings,
            { width: 1200, height: parseInt(currentSection.height) || 400 } // 容器尺寸
          )

          // 计算吸附位置
          const snapResult = calculateSnapPosition(currentRect, guides)

          if (snapResult.activeGuides.length > 0) {
            nextTop = snapResult.position.top
            nextLeft = snapResult.position.left

            // 设置参考线显示
            const vGuides = snapResult.activeGuides
              .filter(g => g.x !== undefined)
              .map(g => ({ x: g.x }))
            const hGuides = snapResult.activeGuides
              .filter(g => g.y !== undefined)
              .map(g => ({ y: g.y }))

            setGuides({ v: vGuides, h: hGuides })
          } else {
            setGuides({ v: [], h: [] })
          }
        }

        // 多选：批量移动
        const movingIds = selectedElements.length > 1 && selectedElements.includes(element.id)
          ? selectedElements
          : [element.id]
        // 使用各自起始 margin + dx/dy 来更新，保持相对位置
        const deltaX = nextLeft - startMarginRef.current.left
        const deltaY = nextTop - startMarginRef.current.top
        movingIds.forEach(id => {
          const base = multiStartMarginsRef.current[id] || { top: 0, left: 0 }
          const mTop = Math.max(0, Math.round(base.top + deltaY))
          const mLeft = Math.max(0, Math.round(base.left + deltaX))
          updateElement(id, { margin: { top: mTop, left: mLeft } }, { commitHistory: false })
        })
    }
    const onUp = () => {
      draggingRef.current = false
      setIsDragging(false)
      useEditorStore.getState().setIsDraggingElement(false)
      clearGuides()

      // 拖动结束时重新显示选中框
      useEditorStore.getState().setShowSelection(true)
      // 恢复文本选择
      document.body.style.userSelect = prevUserSelectRef.current || ''
      document.body.style.webkitUserSelect = prevUserSelectRef.current || ''
      window.removeEventListener('mousemove', onMove)
      window.removeEventListener('mouseup', onUp)
    }
    window.addEventListener('mousemove', onMove)
    window.addEventListener('mouseup', onUp)
  }



  // 包装层样式：按照技术文档使用绝对定位，margin数据转换为top/left
  const getWrapperStyle = () => {
    const m = element.margin || { top: 0, left: 0, right: 0, bottom: 0 }
    return {
      // 使用绝对定位实现真正的层叠效果，不影响文档流
      position: 'absolute',
      top: `${m.top || 0}px`,
      left: `${m.left || 0}px`,
      // 保持zIndex层级管理，确保在元素层内的相对层级
      zIndex: element.zIndex || 1,
      cursor: draggingRef.current ? 'grabbing' : 'grab',
      // 确保元素可以被选中和交互
      userSelect: 'none',
      transformOrigin: 'center center',
    }
  }

  // 元素自身样式（宽高与外观，不包含定位）
  const getElementStyle = () => ({
    ...element.style,
    width: element.size?.width || 'auto',
    height: element.size?.height || 'auto',
    display: 'block',
    // 确保元素内容不会影响布局
    boxSizing: 'border-box',
  })

  // 取消元素内的尺寸控制，统一由 SelectionOverlay 的 8 控制点处理

  // 渲染不同类型的元素内容（不再需要应用样式，样式已在外层容器处理）
  const renderElementContent = () => {
    switch (element.type) {
      case ELEMENT_TYPES.TEXT:
        return (
          <div>
            {element.content || '文本内容'}
          </div>
        )

      case ELEMENT_TYPES.IMAGE:
        return (
          <img
            src={element.src || 'https://via.placeholder.com/150'}
            alt={element.alt || '图片'}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
        )

      case ELEMENT_TYPES.BUTTON:
        return (
          <button style={{ width: '100%', height: '100%' }}>
            {element.content || '按钮'}
          </button>
        )

      case ELEMENT_TYPES.CONTAINER:
        return (
          <div
            ref={(node) => {
              containerDropRef.current = node
              dropInto(node)
            }}
            style={{
              width: '100%',
              height: '100%',
              minHeight: '100px',
              border: isDropOver && canDropInto ? '2px dashed #22c55e' : '2px dashed #d1d5db',
              backgroundColor: isDropOver && canDropInto ? '#f0fdf4' : 'transparent',
              position: 'relative', // 为子元素提供定位上下文
            }}
          >
            {/* 渲染容器内的子元素 */}
            {element.children?.map((child) => (
              <CanvasElement key={child.id} element={child} />
            ))}

            {/* 空容器提示 */}
            {(!element.children || element.children.length === 0) && (
              <div className="flex items-center justify-center h-full text-gray-500 text-sm">
                拖拽元素到这里
              </div>
            )}
          </div>
        )

      case ELEMENT_TYPES.FORM:
        return (
          <form style={{ width: '100%', height: '100%' }}>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="姓名"
                className="w-full p-2 border border-gray-300 rounded"
                readOnly
              />
              <input
                type="email"
                placeholder="邮箱"
                className="w-full p-2 border border-gray-300 rounded"
                readOnly
              />
              <button
                type="button"
                className="w-full p-2 bg-blue-500 text-white rounded"
              >
                提交
              </button>
            </div>
          </form>
        )

      case ELEMENT_TYPES.VIDEO:
        return (
          <video
            src={element.src}
            style={{ width: '100%', height: '100%' }}
            muted
          >
            您的浏览器不支持视频播放
          </video>
        )

      case ELEMENT_TYPES.GALLERY:
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <div className="grid grid-cols-2 gap-2 h-full">
              {[1, 2, 3, 4].map((i) => (
                <img
                  key={i}
                  src={`https://via.placeholder.com/150?text=图片${i}`}
                  alt={`图片 ${i}`}
                  className="w-full h-full object-cover rounded"
                />
              ))}
            </div>
          </div>
        )

      case ELEMENT_TYPES.MAP:
        return (
          <div
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '200px',
            }}
          >
            <div className="text-gray-500 text-center">
              <div className="text-2xl mb-2">🗺️</div>
              <div>地图组件</div>
            </div>
          </div>
        )

      default:
        return (
          <div style={{ width: '100%', height: '100%' }}>
            未知元素类型: {element.type}
          </div>
        )
    }
  }

  // console.log('CanvasElement rendering:', { id: element.id, type: element.type, style: getElementStyle() })

  return (
    <>
      <div
        data-element-id={element.id}
        className={`design-element element-wrapper ${isDragging ? 'dragging' : ''}`}
        style={getWrapperStyle()}
        onClick={(e) => {
          e.stopPropagation()
          selectElement(element.id)
        }}
        onMouseEnter={() => {
          // 悬停时设置为悬停元素，SelectionOverlay会显示预览
          useEditorStore.getState().setHoveredElement(element.id)
        }}
        onMouseLeave={() => {
          // 离开时清除悬停状态
          useEditorStore.getState().setHoveredElement(null)
        }}
        onMouseDown={onDragMouseDown}
        ref={wrapperRef}
      >
        <div className="element-content" style={getElementStyle()}>
          {renderElementContent()}
        </div>
      </div>
    </>
  )
}

export default CanvasElement
