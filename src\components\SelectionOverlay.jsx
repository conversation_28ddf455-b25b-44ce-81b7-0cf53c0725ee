import React from 'react'
import { createPortal } from 'react-dom'
import { useEditorStore } from '../store/editorStore'

const SelectionOverlay = () => {
  const selectedElement = useEditorStore(s => s.selectedElement)
  const selectedElements = useEditorStore(s => s.selectedElements)
  const hoveredElement = useEditorStore(s => s.hoveredElement)
  const isPreviewMode = useEditorStore(s => s.isPreviewMode)
  const isDraggingElement = useEditorStore(s => s.isDraggingElement)
  const showSelection = useEditorStore(s => s.showSelection)
  const { updateElement, setIsResizingElement } = useEditorStore()
  const guides = useEditorStore(s => s.guides)
  const [rect, setRect] = React.useState(null)
  const [groupRect, setGroupRect] = React.useState(null)
  const [hoverRect, setHoverRect] = React.useState(null)
  const dragStartRef = React.useRef(null)
  // 放在条件渲染之前，避免 Hooks 顺序变化
  const startRef = React.useRef({
    mouseX: 0,
    mouseY: 0,
    width: 0,
    height: 0,
    top: 0,
    left: 0,
  })

  // 获取当前选中元素所在区段的 host
  const getSelectionHost = React.useCallback(() => {
    if (!selectedElement) return null
    const el = document.querySelector(`[data-element-id="${selectedElement}"]`)
    const section = el?.closest('[data-section-id]')
    if (section) {
      const sectionId = section.getAttribute('data-section-id')
      return document.getElementById(`selection-overlay-host-${sectionId}`)
    }
    return null
  }, [selectedElement])

  const updateRect = React.useCallback(() => {
    if (!selectedElement && selectedElements.length === 0) {
      setRect(null)
      setGroupRect(null)
      return
    }

    if (selectedElements.length > 1) {
      // 多选情况：找到第一个元素的 host
      const firstEl = selectedElements[0] ? document.querySelector(`[data-element-id="${selectedElements[0]}"]`) : null
      const section = firstEl?.closest('[data-section-id]')
      const sectionId = section?.getAttribute('data-section-id')
      const host = sectionId ? document.getElementById(`selection-overlay-host-${sectionId}`) : null
      const hostRect = host?.getBoundingClientRect()

      const els = selectedElements.map(id => document.querySelector(`[data-element-id="${id}"]`)).filter(Boolean)
      if (els.length === 0) { setGroupRect(null); setRect(null); return }
      const rects = els.map(n => n.getBoundingClientRect())
      const left = Math.min(...rects.map(r => r.left))
      const top = Math.min(...rects.map(r => r.top))
      const right = Math.max(...rects.map(r => r.right))
      const bottom = Math.max(...rects.map(r => r.bottom))
      const offsetLeft = hostRect ? left - hostRect.left : left
      const offsetTop = hostRect ? top - hostRect.top : top
      setGroupRect({ top: offsetTop, left: offsetLeft, width: right - left, height: bottom - top })
      setRect(null)
      return
    }

    const el = selectedElement ? document.querySelector(`[data-element-id="${selectedElement}"]`) : null
    if (el) {
      const r = el.getBoundingClientRect()
      const host = getSelectionHost()
      const hostRect = host?.getBoundingClientRect()
      const offsetTop = hostRect ? r.top - hostRect.top : r.top
      const offsetLeft = hostRect ? r.left - hostRect.left : r.left
      setRect({ top: offsetTop, left: offsetLeft, width: r.width, height: r.height })
      setGroupRect(null)
    } else {
      setRect(null)
      setGroupRect(null)
    }
  }, [selectedElement, selectedElements, getSelectionHost])

  // 更新悬停元素的选中框
  const updateHoverRect = React.useCallback(() => {
    if (!hoveredElement || selectedElement === hoveredElement) {
      setHoverRect(null)
      return
    }

    const el = document.querySelector(`[data-element-id="${hoveredElement}"]`)
    if (el) {
      const r = el.getBoundingClientRect()
      const section = el.closest('[data-section-id]')
      const sectionId = section?.getAttribute('data-section-id')
      const host = sectionId ? document.getElementById(`selection-overlay-host-${sectionId}`) : null
      const hostRect = host?.getBoundingClientRect()
      const offsetTop = hostRect ? r.top - hostRect.top : r.top
      const offsetLeft = hostRect ? r.left - hostRect.left : r.left
      setHoverRect({ top: offsetTop, left: offsetLeft, width: r.width, height: r.height })
    } else {
      setHoverRect(null)
    }
  }, [hoveredElement, selectedElement])

  // 监听悬停元素变化
  React.useEffect(() => {
    updateHoverRect()
  }, [hoveredElement, updateHoverRect])

  React.useEffect(() => {
    updateRect()
    const scroller = document.querySelector('[data-canvas-scroller]')
    const el = selectedElement ? document.querySelector(`[data-element-id="${selectedElement}"]`) : null

    window.addEventListener('resize', updateRect)
    window.addEventListener('scroll', updateRect, true)
    if (scroller) scroller.addEventListener('scroll', updateRect, { passive: true })

    // 监听元素尺寸/位置变化
    let ro
    if (el && 'ResizeObserver' in window) {
      ro = new ResizeObserver(() => updateRect())
      ro.observe(el)
    }

    // 当多选时，为了始终渲染外接框，这里也强制下一帧刷新
    const raf1 = requestAnimationFrame(() => updateRect())

    return () => {
      window.removeEventListener('resize', updateRect)
      window.removeEventListener('scroll', updateRect, true)
      if (scroller) scroller.removeEventListener('scroll', updateRect)
      if (ro) ro.disconnect()
      cancelAnimationFrame(raf1)
    }
  }, [updateRect, selectedElement, selectedElements])

  // 在调整尺寸时使用 rAF 实时刷新，保证选中框紧跟元素
  const isResizingElement = useEditorStore(s => s.isResizingElement)
  React.useEffect(() => {
    if (!isResizingElement) return
    let rafId = 0
    const loop = () => {
      updateRect()
      rafId = requestAnimationFrame(loop)
    }
    loop()
    return () => cancelAnimationFrame(rafId)
  }, [isResizingElement, updateRect])

  // 在拖动时使用 rAF 实时刷新，保证选中框紧跟元素
  React.useEffect(() => {
    if (!isDraggingElement) return
    let rafId = 0
    const loop = () => {
      updateRect()
      rafId = requestAnimationFrame(loop)
    }
    loop()
    return () => cancelAnimationFrame(rafId)
  }, [isDraggingElement, updateRect])

  // 拖动结束的对齐修正：在下一帧和再下一帧各刷新一次，避免出现分离
  React.useEffect(() => {
    if (isDraggingElement) return
    // 立刻刷新
    updateRect()
    // 下一帧刷新
    requestAnimationFrame(() => updateRect())
    // 再下一次微任务，确保 DOM 完全稳定
    setTimeout(() => updateRect(), 0)
  }, [isDraggingElement, updateRect])

  // 全局原生拖拽（来自左侧面板）检测，拖拽进行时隐藏选中框避免影响放置
  const [globalDragging, setGlobalDragging] = React.useState(false)
  React.useEffect(() => {
    const onStart = () => setGlobalDragging(true)
    const onEnd = () => setGlobalDragging(false)
    window.addEventListener('dragstart', onStart)
    window.addEventListener('dragend', onEnd)
    window.addEventListener('drop', onEnd)
    return () => {
      window.removeEventListener('dragstart', onStart)
      window.removeEventListener('dragend', onEnd)
      window.removeEventListener('drop', onEnd)
    }
  }, [])

  // 拖动现有元素 / 外部拖拽 / 预览模式 / showSelection为false 时隐藏
  if (isPreviewMode || !showSelection) return null
  if (!rect && !(selectedElements.length > 1 && groupRect) && !hoverRect) return null

  const getCurrentMarginAndSize = () => {
    // 新架构：直接从元素数据获取 margin 和 size，因为现在 margin 就是定位信息
    const state = useEditorStore.getState()
    const data = state.selectedElementData

    if (data) {
      const margin = data.margin || { top: 0, left: 0 }
      const size = data.size || { width: 'auto', height: 'auto' }

      // 如果 size 是 auto，从 DOM 获取实际尺寸
      let width = parseInt(size.width) || 0
      let height = parseInt(size.height) || 0

      if (size.width === 'auto' || size.height === 'auto') {
        const el = document.querySelector(`[data-element-id="${selectedElement}"]`)
        if (el) {
          const rect = el.getBoundingClientRect()
          if (size.width === 'auto') width = Math.round(rect.width)
          if (size.height === 'auto') height = Math.round(rect.height)
        }
      }

      return {
        top: margin.top || 0,
        left: margin.left || 0,
        width: width || 100,
        height: height || 40,
      }
    }

    // 回退方案
    return { top: 0, left: 0, width: 100, height: 40 }
  }

  const beginResize = (e, dir) => {
    e.preventDefault()
    e.stopPropagation()
    const host = document.getElementById('selection-overlay-host')
    const hostRect = host?.getBoundingClientRect()

    // 多选批量缩放：记录群组与每个元素的起始信息
    const store = useEditorStore.getState()
    if (selectedElements.length > 1) {
      const nodes = selectedElements
        .map(id => document.querySelector(`[data-element-id="${id}"]`))
        .filter(Boolean)
      if (nodes.length === 0) return
      const rects = nodes.map(n => n.getBoundingClientRect())
      const leftAbs = Math.min(...rects.map(r => r.left))
      const topAbs = Math.min(...rects.map(r => r.top))
      const rightAbs = Math.max(...rects.map(r => r.right))
      const bottomAbs = Math.max(...rects.map(r => r.bottom))
      const groupLeft = hostRect ? leftAbs - hostRect.left : leftAbs
      const groupTop = hostRect ? topAbs - hostRect.top : topAbs
      const groupWidth = rightAbs - leftAbs
      const groupHeight = bottomAbs - topAbs

      const items = {}
      nodes.forEach((node, idx) => {
        const id = selectedElements[idx]
        const elRect = rects[idx]
        const section = node.closest('[data-section-id]')
        const secRectAbs = section?.getBoundingClientRect()
        const secTop = hostRect ? (secRectAbs.top - hostRect.top) : secRectAbs.top
        const secLeft = hostRect ? (secRectAbs.left - hostRect.left) : secRectAbs.left
        const elLeft = hostRect ? (elRect.left - hostRect.left) : elRect.left
        const elTop = hostRect ? (elRect.top - hostRect.top) : elRect.top
        items[id] = {
          baseLeft: elLeft,
          baseTop: elTop,
          baseWidth: elRect.width,
          baseHeight: elRect.height,
          sectionTop: secTop,
          sectionLeft: secLeft,
        }
      })

      startRef.current = {
        mouseX: e.clientX,
        mouseY: e.clientY,
        shiftKey: e.shiftKey, // 等比
        altKey: e.altKey,     // 以中心为基点
        group: {
          top: groupTop,
          left: groupLeft,
          width: groupWidth,
          height: groupHeight,
          items,
        },
      }
    } else {
      // 单元素缩放：以元素相对区段的实际可视位置作为基准，避免使用 margin.left/top 为 0 时导致跳到左上角
      const el = selectedElement ? document.querySelector(`[data-element-id="${selectedElement}"]`) : null
      const section = el?.closest('[data-section-id]')
      const secRect = section?.getBoundingClientRect()
      const elRect = el?.getBoundingClientRect()
      const baseLeft = elRect && secRect ? (elRect.left - secRect.left) : 0
      const baseTop = elRect && secRect ? (elRect.top - secRect.top) : 0
      const baseWidth = elRect ? elRect.width : 100
      const baseHeight = elRect ? elRect.height : 40
      startRef.current = {
        mouseX: e.clientX,
        mouseY: e.clientY,
        shiftKey: e.shiftKey,
        altKey: e.altKey,
        width: baseWidth,
        height: baseHeight,
        top: baseTop,
        left: baseLeft,
        sectionTop: secRect?.top || 0,
        sectionLeft: secRect?.left || 0,
      }
    }
    setIsResizingElement(true)

    let rafId = 0
    const onMove = (ev) => {
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => {
        const dx = ev.clientX - startRef.current.mouseX
        const dy = ev.clientY - startRef.current.mouseY

        // 多选批量缩放
        if (selectedElements.length > 1 && startRef.current.group) {
          const g = startRef.current.group
          let nextLeft = g.left
          let nextTop = g.top
          let nextWidth = g.width
          let nextHeight = g.height
          if (dir.includes('e')) nextWidth = Math.max(10, g.width + dx)
          if (dir.includes('s')) nextHeight = Math.max(10, g.height + dy)
          if (dir.includes('w')) { nextWidth = Math.max(10, g.width - dx); nextLeft = Math.max(0, g.left + dx) }
          if (dir.includes('n')) { nextHeight = Math.max(10, g.height - dy); nextTop = Math.max(0, g.top + dy) }

          // Shift 等比：以改变较大比例为主，另一个维度跟随
          if (startRef.current.shiftKey) {
            const ratio = g.width / Math.max(1, g.height)
            if (dir === 'e' || dir === 'w') {
              nextHeight = Math.max(10, nextWidth / Math.max(0.0001, ratio))
            } else if (dir === 'n' || dir === 's') {
              nextWidth = Math.max(10, nextHeight * ratio)
            } else {
              // 角点：取变化更大的那个来驱动等比
              const sx = nextWidth / Math.max(1, g.width)
              const sy = nextHeight / Math.max(1, g.height)
              const s = Math.max(sx, sy)
              nextWidth = Math.max(10, g.width * s)
              nextHeight = Math.max(10, g.height * s)
              // 同步 left/top 使等比以对角方向扩展
              if (dir.includes('w')) nextLeft = g.left + (g.width - nextWidth)
              if (dir.includes('n')) nextTop = g.top + (g.height - nextHeight)
            }
          }

          // Alt 中心缩放：保持中心不动
          if (startRef.current.altKey) {
            const cx = g.left + g.width / 2
            const cy = g.top + g.height / 2
            const nx = nextWidth / 2
            const ny = nextHeight / 2
            nextLeft = cx - nx
            nextTop = cy - ny
          }

          const scaleX = nextWidth / Math.max(1, g.width)
          const scaleY = nextHeight / Math.max(1, g.height)

          // 本地刷新群组外框
          setGroupRect({ top: nextTop, left: nextLeft, width: nextWidth, height: nextHeight })

          // 按比例更新每个元素的位置与尺寸
          Object.entries(g.items).forEach(([id, item]) => {
            const offsetLeft = item.baseLeft - g.left
            const offsetTop = item.baseTop - g.top
            const newLeftAbs = nextLeft + offsetLeft * scaleX
            const newTopAbs = nextTop + offsetTop * scaleY
            const newWidth = Math.max(10, item.baseWidth * scaleX)
            const newHeight = Math.max(10, item.baseHeight * scaleY)
            const marginLeft = Math.max(0, Math.round(newLeftAbs - item.sectionLeft))
            const marginTop = Math.max(0, Math.round(newTopAbs - item.sectionTop))
            updateElement(id, {
              size: { width: `${Math.round(newWidth)}px`, height: `${Math.round(newHeight)}px` },
              margin: { top: marginTop, left: marginLeft },
            }, { commitHistory: false })
          })
          return
        }

        // 单元素缩放 - 新架构：直接操作 margin 和 size
        let nextTop = startRef.current.top
        let nextLeft = startRef.current.left
        let nextWidth = startRef.current.width
        let nextHeight = startRef.current.height

        if (dir.includes('e')) nextWidth = Math.max(10, startRef.current.width + dx)
        if (dir.includes('s')) nextHeight = Math.max(10, startRef.current.height + dy)
        if (dir.includes('w')) {
          nextWidth = Math.max(10, startRef.current.width - dx)
          nextLeft = Math.max(0, startRef.current.left + dx)
        }
        if (dir.includes('n')) {
          nextHeight = Math.max(10, startRef.current.height - dy)
          nextTop = Math.max(0, startRef.current.top + dy)
        }

        // Shift 等比
        if (startRef.current.shiftKey) {
          const ratio = Math.max(0.0001, startRef.current.width / Math.max(1, startRef.current.height))
          if (dir === 'e' || dir === 'w') {
            nextHeight = Math.max(10, nextWidth / ratio)
          } else if (dir === 'n' || dir === 's') {
            nextWidth = Math.max(10, nextHeight * ratio)
          } else {
            const sx = nextWidth / Math.max(1, startRef.current.width)
            const sy = nextHeight / Math.max(1, startRef.current.height)
            const s = Math.max(sx, sy)
            nextWidth = Math.max(10, startRef.current.width * s)
            nextHeight = Math.max(10, startRef.current.height * s)
            if (dir.includes('w')) nextLeft = startRef.current.left + (startRef.current.width - nextWidth)
            if (dir.includes('n')) nextTop = startRef.current.top + (startRef.current.height - nextHeight)
          }
        }

        // Alt 中心缩放
        if (startRef.current.altKey) {
          const cx = startRef.current.left + startRef.current.width / 2
          const cy = startRef.current.top + startRef.current.height / 2
          nextLeft = Math.max(0, cx - nextWidth / 2)
          nextTop = Math.max(0, cy - nextHeight / 2)
        }

        // 更新选中框位置（基于新的 host 坐标系）
        const host = getSelectionHost()
        if (host) {
          setRect({
            top: nextTop,
            left: nextLeft,
            width: nextWidth,
            height: nextHeight
          })
        }

        // 更新元素数据：margin 用于定位，size 用于尺寸
        updateElement(selectedElement, {
          size: { width: `${Math.round(nextWidth)}px`, height: `${Math.round(nextHeight)}px` },
          margin: { top: Math.round(nextTop), left: Math.round(nextLeft) },
        }, { commitHistory: false })
      })
    }
    const onUp = () => {
      setIsResizingElement(false)
      updateRect()
      window.removeEventListener('mousemove', onMove)
      window.removeEventListener('mouseup', onUp)
    }
    window.addEventListener('mousemove', onMove)
    window.addEventListener('mouseup', onUp)
  }

  const handleStyleBase = {
    position: 'absolute',
    width: 8,
    height: 8,
    backgroundColor: '#ffffff',
    border: '1.5px solid #116dff',
    borderRadius: 2,
    boxShadow: '0 0 0 1px rgba(17, 109, 255, 0.15)',
    pointerEvents: 'auto',
  }

  const beginGroupDrag = (e) => {
    if (!(selectedElements.length > 1 && groupRect)) return
    e.preventDefault()
    e.stopPropagation()
    const store = useEditorStore.getState()
    store.setIsDraggingElement(true)
    // 群组拖拽不需要 host 引用，直接操作元素数据
    const startX = e.clientX
    const startY = e.clientY
    const pages = store.pages
    const active = pages.find(p => p.active)
    const sectionElements = active?.sections?.flatMap(s => s.elements || []) || []
    const map = {}
    selectedElements.forEach(id => {
      const el = sectionElements.find(e => e.id === id)
      const m = el?.margin || { top: 0, left: 0 }
      map[id] = { top: m.top || 0, left: m.left || 0 }
    })
    dragStartRef.current = { startX, startY, baseMargins: map }
    let rafId = 0
    const onMove = (ev) => {
      const dx = ev.clientX - dragStartRef.current.startX
      const dy = ev.clientY - dragStartRef.current.startY
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => {
        selectedElements.forEach(id => {
          const base = dragStartRef.current.baseMargins[id] || { top: 0, left: 0 }
          const nextTop = Math.max(0, Math.round(base.top + dy))
          const nextLeft = Math.max(0, Math.round(base.left + dx))
          updateElement(id, { margin: { top: nextTop, left: nextLeft } }, { commitHistory: false })
        })
        // 刷新外接框位置
        updateRect()
      })
    }
    const onUp = () => {
      store.setIsDraggingElement(false)
      window.removeEventListener('mousemove', onMove)
      window.removeEventListener('mouseup', onUp)
      updateRect()
    }
    window.addEventListener('mousemove', onMove)
    window.addEventListener('mouseup', onUp)
  }

  const overlay = (
    <div className="pointer-events-none absolute z-[60]" style={{ top: 0, left: 0 }}>
      {(selectedElements.length > 1 && groupRect) ? (
        <div
          className="absolute rounded-sm"
          style={{
            top: groupRect.top,
            left: groupRect.left,
            width: groupRect.width,
            height: groupRect.height,
            border: '2px solid #116dff',
            boxShadow: '0 0 0 1px rgba(17,109,255,0.25), 0 0 0 4px rgba(17,109,255,0.08)',
            pointerEvents: 'none'
          }}
        >
          {/* 拖拽区域 - 只在选中框的边框区域启用pointer-events */}
          <div
            className="absolute inset-0"
            style={{
              pointerEvents: 'auto',
              cursor: 'move'
            }}
            onMouseDown={beginGroupDrag}
          />
          {/* 群组 8 控制点 - 批量缩放（等比/居中后续增强） */}
          <div
            style={{ ...handleStyleBase, top: -4, left: '50%', transform: 'translateX(-50%)', cursor: 'ns-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'n')}
          />
          <div
            style={{ ...handleStyleBase, bottom: -4, left: '50%', transform: 'translateX(-50%)', cursor: 'ns-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 's')}
          />
          <div
            style={{ ...handleStyleBase, left: -4, top: '50%', transform: 'translateY(-50%)', cursor: 'ew-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'w')}
          />
          <div
            style={{ ...handleStyleBase, right: -4, top: '50%', transform: 'translateY(-50%)', cursor: 'ew-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'e')}
          />
          <div
            style={{ ...handleStyleBase, top: -4, left: -4, cursor: 'nwse-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'nw')}
          />
          <div
            style={{ ...handleStyleBase, top: -4, right: -4, cursor: 'nesw-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'ne')}
          />
          <div
            style={{ ...handleStyleBase, bottom: -4, left: -4, cursor: 'nesw-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'sw')}
          />
          <div
            style={{ ...handleStyleBase, bottom: -4, right: -4, cursor: 'nwse-resize' }}
            className="pointer-events-auto"
            onMouseDown={(e) => beginResize(e, 'se')}
          />
        </div>
      ) : (
        rect && (
          <div
            className="absolute rounded-sm pointer-events-none"
            style={{
              top: rect.top,
              left: rect.left,
              width: rect.width,
              height: rect.height,
              border: '2px solid #116dff',
              boxShadow: '0 0 0 1px rgba(17,109,255,0.25), 0 0 0 4px rgba(17,109,255,0.08)'
            }}
          >
        {/* 8 个控制点：n, s, e, w, ne, nw, se, sw */}
        <div
          style={{ ...handleStyleBase, top: -4, left: '50%', transform: 'translateX(-50%)', cursor: 'ns-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'n')}
        />
        <div
          style={{ ...handleStyleBase, bottom: -4, left: '50%', transform: 'translateX(-50%)', cursor: 'ns-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 's')}
        />
        <div
          style={{ ...handleStyleBase, left: -4, top: '50%', transform: 'translateY(-50%)', cursor: 'ew-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'w')}
        />
        <div
          style={{ ...handleStyleBase, right: -4, top: '50%', transform: 'translateY(-50%)', cursor: 'ew-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'e')}
        />
        <div
          style={{ ...handleStyleBase, top: -4, left: -4, cursor: 'nwse-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'nw')}
        />
        <div
          style={{ ...handleStyleBase, top: -4, right: -4, cursor: 'nesw-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'ne')}
        />
        <div
          style={{ ...handleStyleBase, bottom: -4, left: -4, cursor: 'nesw-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'sw')}
        />
        <div
          style={{ ...handleStyleBase, bottom: -4, right: -4, cursor: 'nwse-resize' }}
          className="pointer-events-auto"
          onMouseDown={(e) => beginResize(e, 'se')}
          />
        </div>
        )
      )}
      {/* 悬停选中框 - 只显示边框，不显示控制点 */}
      {hoverRect && !selectedElement && (
        <div
          className="absolute rounded-sm pointer-events-none"
          style={{
            top: hoverRect.top,
            left: hoverRect.left,
            width: hoverRect.width,
            height: hoverRect.height,
            border: '1px dashed #116dff',
            opacity: 0.6
          }}
        />
      )}

      {/* 参考线 */}
      {guides.v.map((g, i) => (
        <div key={`vg-${i}`} className="absolute" style={{ top: 0, bottom: 0, left: g.x, width: 1, backgroundColor: 'rgba(17,109,255,0.55)' }} />
      ))}
      {guides.h.map((g, i) => (
        <div key={`hg-${i}`} className="absolute" style={{ left: 0, right: 0, top: g.y, height: 1, backgroundColor: 'rgba(17,109,255,0.55)' }} />
      ))}
    </div>
  )

  const host = getSelectionHost()
  return host ? createPortal(overlay, host) : null
}

export default SelectionOverlay

