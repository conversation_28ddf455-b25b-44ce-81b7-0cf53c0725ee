/**
 * 架构测试 - 验证新的 DOM 结构和定位系统
 */

// 测试新的 DOM 结构
export const testDOMStructure = () => {
  console.log('🧪 测试 DOM 结构...')
  
  // 检查区段容器
  const sections = document.querySelectorAll('[data-section-id]')
  console.log(`✅ 找到 ${sections.length} 个区段`)
  
  sections.forEach((section, index) => {
    const sectionId = section.getAttribute('data-section-id')
    console.log(`📦 区段 ${index + 1}: ${sectionId}`)
    
    // 检查元素包装器
    const elementWrappers = section.querySelectorAll('.element-wrapper')
    console.log(`  📄 元素包装器: ${elementWrappers.length} 个`)
    
    elementWrappers.forEach((wrapper, wIndex) => {
      const elementId = wrapper.getAttribute('data-element-id')
      const style = window.getComputedStyle(wrapper)
      console.log(`    🎯 元素 ${wIndex + 1} (${elementId}):`)
      console.log(`      - margin: ${style.marginTop} ${style.marginLeft}`)
      console.log(`      - position: ${style.position}`)
      
      // 检查元素内容
      const content = wrapper.querySelector('.element-content')
      if (content) {
        console.log(`      - 内容容器: ✅`)
      }
    })
    
    // 检查选中框 host
    const selectionHost = document.getElementById(`selection-overlay-host-${sectionId}`)
    if (selectionHost) {
      console.log(`  🎯 选中框 host: ✅`)
    } else {
      console.log(`  ❌ 选中框 host: 未找到`)
    }
    
    // 检查容器
    const containers = section.querySelectorAll('[data-container-id]')
    console.log(`  📦 容器: ${containers.length} 个`)
  })
}

// 测试定位系统
export const testPositioningSystem = () => {
  console.log('🧪 测试定位系统...')
  
  const elements = document.querySelectorAll('[data-element-id]')
  
  elements.forEach((element, index) => {
    const elementId = element.getAttribute('data-element-id')
    const style = window.getComputedStyle(element)
    
    console.log(`🎯 元素 ${index + 1} (${elementId}):`)
    console.log(`  - 定位方式: ${style.position}`)
    console.log(`  - margin: ${style.marginTop} ${style.marginLeft} ${style.marginRight} ${style.marginBottom}`)
    console.log(`  - top/left: ${style.top} ${style.left}`)
    
    // 验证是否使用了正确的定位方式
    if (style.position === 'relative' && (style.marginTop !== '0px' || style.marginLeft !== '0px')) {
      console.log(`  ✅ 使用 margin 定位`)
    } else if (style.position === 'absolute') {
      console.log(`  ⚠️  使用绝对定位 (可能是选中框或特殊元素)`)
    } else {
      console.log(`  ❓ 定位方式不明确`)
    }
  })
}

// 测试选中框系统
export const testSelectionSystem = () => {
  console.log('🧪 测试选中框系统...')
  
  // 查找所有选中框 host
  const hosts = document.querySelectorAll('[id^="selection-overlay-host-"]')
  console.log(`🎯 选中框 host: ${hosts.length} 个`)
  
  hosts.forEach((host, index) => {
    const hostId = host.id
    const sectionId = hostId.replace('selection-overlay-host-', '')
    console.log(`  📦 Host ${index + 1}: ${hostId} (区段: ${sectionId})`)
    
    // 检查是否有选中框渲染在其中
    const selectionBoxes = host.querySelectorAll('[style*="border"]')
    console.log(`    🎯 选中框: ${selectionBoxes.length} 个`)
  })
}

// 测试对齐系统
export const testAlignmentSystem = () => {
  console.log('🧪 测试对齐系统...')
  
  // 查找参考线
  const guides = document.querySelectorAll('[style*="rgba(17,109,255,0.55)"]')
  console.log(`📏 参考线: ${guides.length} 个`)
  
  if (guides.length > 0) {
    guides.forEach((guide, index) => {
      const style = window.getComputedStyle(guide)
      const isVertical = style.width === '1px'
      const isHorizontal = style.height === '1px'
      
      console.log(`  📏 参考线 ${index + 1}: ${isVertical ? '垂直' : isHorizontal ? '水平' : '未知'}`)
    })
  }
}

// 测试容器系统
export const testContainerSystem = () => {
  console.log('🧪 测试容器系统...')
  
  const containers = document.querySelectorAll('[data-container-id]')
  console.log(`📦 容器: ${containers.length} 个`)
  
  containers.forEach((container, index) => {
    const containerId = container.getAttribute('data-container-id')
    const style = window.getComputedStyle(container)
    
    console.log(`  📦 容器 ${index + 1} (${containerId}):`)
    console.log(`    - display: ${style.display}`)
    console.log(`    - position: ${style.position}`)
    
    // 检查容器内的元素
    const childElements = container.querySelectorAll('[data-element-id]')
    console.log(`    - 子元素: ${childElements.length} 个`)
  })
}

// 运行所有测试
export const runAllTests = () => {
  console.log('🚀 开始架构测试...')
  console.log('=' * 50)
  
  testDOMStructure()
  console.log('')
  
  testPositioningSystem()
  console.log('')
  
  testSelectionSystem()
  console.log('')
  
  testAlignmentSystem()
  console.log('')
  
  testContainerSystem()
  console.log('')
  
  console.log('✅ 架构测试完成!')
}

// 在开发环境中自动运行测试
if (import.meta.env.DEV) {
  // 等待 DOM 加载完成后运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(runAllTests, 1000) // 延迟 1 秒确保组件渲染完成
    })
  } else {
    setTimeout(runAllTests, 1000)
  }
  
  // 将测试函数暴露到全局，方便在控制台调用
  window.architectureTest = {
    runAllTests,
    testDOMStructure,
    testPositioningSystem,
    testSelectionSystem,
    testAlignmentSystem,
    testContainerSystem
  }
}
