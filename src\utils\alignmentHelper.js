/**
 * 对齐和吸附辅助工具
 * 提供元素对齐、参考线、智能吸附等功能
 */

// 吸附阈值（像素）
export const SNAP_THRESHOLD = 8

/**
 * 计算元素的对齐参考线
 * @param {Object} element - 当前元素
 * @param {Array} siblings - 同级元素列表
 * @param {Object} container - 容器信息
 * @returns {Object} 参考线信息 { vertical: [], horizontal: [] }
 */
export const calculateAlignmentGuides = (element, siblings, container) => {
  const guides = { vertical: [], horizontal: [] }
  
  if (!element || !siblings) return guides
  
  const elementRect = getElementRect(element)
  const elementCenter = {
    x: elementRect.left + elementRect.width / 2,
    y: elementRect.top + elementRect.height / 2
  }
  
  // 容器边界参考线
  if (container) {
    guides.vertical.push(
      { x: 0, type: 'container-left' },
      { x: container.width / 2, type: 'container-center' },
      { x: container.width, type: 'container-right' }
    )
    guides.horizontal.push(
      { y: 0, type: 'container-top' },
      { y: container.height / 2, type: 'container-center' },
      { y: container.height, type: 'container-bottom' }
    )
  }
  
  // 兄弟元素参考线
  siblings.forEach(sibling => {
    if (sibling.id === element.id) return
    
    const siblingRect = getElementRect(sibling)
    const siblingCenter = {
      x: siblingRect.left + siblingRect.width / 2,
      y: siblingRect.top + siblingRect.height / 2
    }
    
    // 垂直参考线（左对齐、中心对齐、右对齐）
    guides.vertical.push(
      { x: siblingRect.left, type: 'element-left', elementId: sibling.id },
      { x: siblingCenter.x, type: 'element-center', elementId: sibling.id },
      { x: siblingRect.left + siblingRect.width, type: 'element-right', elementId: sibling.id }
    )
    
    // 水平参考线（顶对齐、中心对齐、底对齐）
    guides.horizontal.push(
      { y: siblingRect.top, type: 'element-top', elementId: sibling.id },
      { y: siblingCenter.y, type: 'element-center', elementId: sibling.id },
      { y: siblingRect.top + siblingRect.height, type: 'element-bottom', elementId: sibling.id }
    )
  })
  
  return guides
}

/**
 * 计算吸附位置
 * @param {Object} elementRect - 当前元素矩形
 * @param {Object} guides - 参考线信息
 * @param {number} threshold - 吸附阈值
 * @returns {Object} 吸附结果 { position: {top, left}, activeGuides: [] }
 */
export const calculateSnapPosition = (elementRect, guides, threshold = SNAP_THRESHOLD) => {
  const result = {
    position: { top: elementRect.top, left: elementRect.left },
    activeGuides: []
  }
  
  const elementCenter = {
    x: elementRect.left + elementRect.width / 2,
    y: elementRect.top + elementRect.height / 2
  }
  
  // 垂直吸附
  let minXDistance = threshold + 1
  let snapX = null
  let activeVerticalGuide = null
  
  guides.vertical.forEach(guide => {
    // 检查左边缘吸附
    const leftDistance = Math.abs(elementRect.left - guide.x)
    if (leftDistance < minXDistance) {
      minXDistance = leftDistance
      snapX = guide.x
      activeVerticalGuide = guide
    }
    
    // 检查中心吸附
    const centerDistance = Math.abs(elementCenter.x - guide.x)
    if (centerDistance < minXDistance) {
      minXDistance = centerDistance
      snapX = guide.x - elementRect.width / 2
      activeVerticalGuide = guide
    }
    
    // 检查右边缘吸附
    const rightDistance = Math.abs(elementRect.left + elementRect.width - guide.x)
    if (rightDistance < minXDistance) {
      minXDistance = rightDistance
      snapX = guide.x - elementRect.width
      activeVerticalGuide = guide
    }
  })
  
  if (snapX !== null && minXDistance <= threshold) {
    result.position.left = Math.max(0, snapX)
    result.activeGuides.push(activeVerticalGuide)
  }
  
  // 水平吸附
  let minYDistance = threshold + 1
  let snapY = null
  let activeHorizontalGuide = null
  
  guides.horizontal.forEach(guide => {
    // 检查顶边缘吸附
    const topDistance = Math.abs(elementRect.top - guide.y)
    if (topDistance < minYDistance) {
      minYDistance = topDistance
      snapY = guide.y
      activeHorizontalGuide = guide
    }
    
    // 检查中心吸附
    const centerDistance = Math.abs(elementCenter.y - guide.y)
    if (centerDistance < minYDistance) {
      minYDistance = centerDistance
      snapY = guide.y - elementRect.height / 2
      activeHorizontalGuide = guide
    }
    
    // 检查底边缘吸附
    const bottomDistance = Math.abs(elementRect.top + elementRect.height - guide.y)
    if (bottomDistance < minYDistance) {
      minYDistance = bottomDistance
      snapY = guide.y - elementRect.height
      activeHorizontalGuide = guide
    }
  })
  
  if (snapY !== null && minYDistance <= threshold) {
    result.position.top = Math.max(0, snapY)
    result.activeGuides.push(activeHorizontalGuide)
  }
  
  return result
}

/**
 * 从元素数据获取矩形信息
 * @param {Object} element - 元素数据
 * @returns {Object} 矩形信息 { top, left, width, height }
 */
export const getElementRect = (element) => {
  const margin = element.margin || { top: 0, left: 0 }
  const size = element.size || { width: 'auto', height: 'auto' }
  
  // 如果尺寸是 auto，需要从 DOM 获取实际尺寸
  let width = parseInt(size.width) || 100
  let height = parseInt(size.height) || 40
  
  if (size.width === 'auto' || size.height === 'auto') {
    const domElement = document.querySelector(`[data-element-id="${element.id}"]`)
    if (domElement) {
      const rect = domElement.getBoundingClientRect()
      if (size.width === 'auto') width = rect.width
      if (size.height === 'auto') height = rect.height
    }
  }
  
  return {
    top: margin.top || 0,
    left: margin.left || 0,
    width,
    height
  }
}

/**
 * 多选元素的对齐操作
 */
export const alignElements = (elements, alignType) => {
  if (!elements || elements.length < 2) return elements
  
  const rects = elements.map(el => ({ ...el, rect: getElementRect(el) }))
  
  switch (alignType) {
    case 'left':
      const leftMost = Math.min(...rects.map(r => r.rect.left))
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, left: leftMost }
      }))
      
    case 'right':
      const rightMost = Math.max(...rects.map(r => r.rect.left + r.rect.width))
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, left: rightMost - r.rect.width }
      }))
      
    case 'top':
      const topMost = Math.min(...rects.map(r => r.rect.top))
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, top: topMost }
      }))
      
    case 'bottom':
      const bottomMost = Math.max(...rects.map(r => r.rect.top + r.rect.height))
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, top: bottomMost - r.rect.height }
      }))
      
    case 'center-horizontal':
      const centerX = rects.reduce((sum, r) => sum + r.rect.left + r.rect.width / 2, 0) / rects.length
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, left: centerX - r.rect.width / 2 }
      }))
      
    case 'center-vertical':
      const centerY = rects.reduce((sum, r) => sum + r.rect.top + r.rect.height / 2, 0) / rects.length
      return rects.map(r => ({
        ...r,
        margin: { ...r.margin, top: centerY - r.rect.height / 2 }
      }))
      
    default:
      return elements
  }
}

/**
 * 分布元素（等间距排列）
 */
export const distributeElements = (elements, distributeType) => {
  if (!elements || elements.length < 3) return elements
  
  const rects = elements.map(el => ({ ...el, rect: getElementRect(el) }))
  
  switch (distributeType) {
    case 'horizontal':
      rects.sort((a, b) => a.rect.left - b.rect.left)
      const totalWidth = rects[rects.length - 1].rect.left - rects[0].rect.left
      const spacing = totalWidth / (rects.length - 1)
      return rects.map((r, index) => ({
        ...r,
        margin: { ...r.margin, left: rects[0].rect.left + spacing * index }
      }))
      
    case 'vertical':
      rects.sort((a, b) => a.rect.top - b.rect.top)
      const totalHeight = rects[rects.length - 1].rect.top - rects[0].rect.top
      const vSpacing = totalHeight / (rects.length - 1)
      return rects.map((r, index) => ({
        ...r,
        margin: { ...r.margin, top: rects[0].rect.top + vSpacing * index }
      }))
      
    default:
      return elements
  }
}
