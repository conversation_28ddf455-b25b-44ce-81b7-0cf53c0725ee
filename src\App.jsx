import React from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { motion } from 'framer-motion'
import TopToolbar from './components/TopToolbar'
import LeftSidebar from './components/LeftSidebar'
import LeftPanel from './components/LeftPanel'
import Canvas from './components/Canvas'
import RightPanel from './components/RightPanel'

import { useEditorStore } from './store/editorStore'
import { useAutoSave } from './hooks/useAutoSave'

function App() {
  const {
    undo,
    redo,
    save,
    deleteSelectedElement,
    selectedElement,
    canUndo,
    canRedo
  } = useEditorStore()

  // 自动保存
  useAutoSave()

  // 键盘快捷键
  useHotkeys('ctrl+z, cmd+z', (e) => {
    e.preventDefault()
    if (canUndo) undo()
  }, { enableOnFormTags: true })

  useHotkeys('ctrl+y, cmd+y, ctrl+shift+z, cmd+shift+z', (e) => {
    e.preventDefault()
    if (canRedo) redo()
  }, { enableOnFormTags: true })

  useHotkeys('ctrl+s, cmd+s', (e) => {
    e.preventDefault()
    save()
  }, { enableOnFormTags: true })

  useHotkeys('delete, backspace', (e) => {
    if (selectedElement && !e.target.isContentEditable) {
      e.preventDefault()
      deleteSelectedElement()
    }
  }, { enableOnFormTags: false })

  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      {/* 顶部工具栏 */}
      <TopToolbar />

      {/* 主工作区 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧工具栏 */}
        <LeftSidebar />

        {/* 左侧面板 */}
        <LeftPanel />

        {/* 中央画布区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Canvas />
        </div>

        {/* 右侧属性面板 */}
        <RightPanel />
      </div>

      {/* 全局状态指示器 */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <div className="bg-white rounded-lg shadow-lg border p-3 flex items-center space-x-2 text-sm">
          <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse-success"></div>
          <span className="text-gray-600">自动保存已启用</span>
        </div>
      </motion.div>


    </div>
  )
}

// 在开发环境中引入架构测试
if (import.meta.env.DEV) {
  import('./test/architecture-test.js')
}

export default App
