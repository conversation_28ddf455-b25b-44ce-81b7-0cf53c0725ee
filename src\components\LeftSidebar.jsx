import React from 'react'
import { motion } from 'framer-motion'
import { 
  Plus, 
  Layers, 
  FileText, 
  Palette, 
  Image,
  Settings
} from 'lucide-react'
import * as Tooltip from '@radix-ui/react-tooltip'
import { useEditorStore, PANEL_TYPES } from '../store/editorStore'

const LeftSidebar = () => {
  const { activePanel, setActivePanel } = useEditorStore()

  const sidebarItems = [
    {
      id: PANEL_TYPES.ELEMENTS,
      icon: Plus,
      label: '添加元素',
      description: '拖拽组件到画布',
    },
    {
      id: PANEL_TYPES.LAYERS,
      icon: Layers,
      label: '图层管理',
      description: '管理页面元素层级',
    },
    {
      id: PANEL_TYPES.PAGES,
      icon: FileText,
      label: '页面管理',
      description: '添加和管理页面',
    },
    {
      id: PANEL_TYPES.THEME,
      icon: Palette,
      label: '主题设置',
      description: '自定义颜色和样式',
    },
    {
      id: PANEL_TYPES.MEDIA,
      icon: Image,
      label: '媒体库',
      description: '管理图片和视频',
    },
  ]

  return (
    <Tooltip.Provider>
      <div className="w-16 bg-gray-800 flex flex-col items-center py-4 space-y-2 border-r border-gray-700">
        {sidebarItems.map((item) => {
          const Icon = item.icon
          const isActive = activePanel === item.id

          return (
            <Tooltip.Root key={item.id}>
              <Tooltip.Trigger asChild>
                <motion.button
                  onClick={() => setActivePanel(item.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`
                    w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-200
                    ${isActive 
                      ? 'bg-primary-600 text-white shadow-lg' 
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
                    }
                  `}
                >
                  <Icon className="w-5 h-5" />
                </motion.button>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content 
                  side="right" 
                  className="bg-gray-900 text-white px-3 py-2 rounded-md shadow-lg max-w-xs z-50"
                >
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs text-gray-400 mt-1">{item.description}</div>
                  <Tooltip.Arrow className="fill-gray-900" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>
          )
        })}

        {/* 分隔线 */}
        <div className="w-8 h-px bg-gray-600 my-4"></div>

        {/* 设置按钮 */}
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-12 h-12 rounded-lg flex items-center justify-center bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-all duration-200"
            >
              <Settings className="w-5 h-5" />
            </motion.button>
          </Tooltip.Trigger>
          <Tooltip.Portal>
            <Tooltip.Content 
              side="right" 
              className="bg-gray-900 text-white px-3 py-2 rounded-md shadow-lg z-50"
            >
              <div className="font-medium">设置</div>
              <div className="text-xs text-gray-400 mt-1">编辑器设置和偏好</div>
              <Tooltip.Arrow className="fill-gray-900" />
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </div>
    </Tooltip.Provider>
  )
}

export default LeftSidebar
