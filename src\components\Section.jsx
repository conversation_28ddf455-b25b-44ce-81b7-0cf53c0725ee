import React, { useRef } from 'react'
import { useDrop } from 'react-dnd'
import { useEditorStore } from '../store/editorStore'
import CanvasElement from './CanvasElement'
import Container from './Container'
import MarqueeSelection from './MarqueeSelection'

const Section = ({ section }) => {
  const { addElement, addSection, selectSection, selectedSection } = useEditorStore()
  const sectionRef = useRef(null)

  // 计算拖拽位置的 margin（相对父容器）
  const computeMargin = (parentRect, clientOffset) => {
    const x = clientOffset.x - parentRect.left
    const y = clientOffset.y - parentRect.top
    const centerX = parentRect.width / 2
    const centerY = parentRect.height / 2
    const useLeft = x <= centerX
    const useTop = y <= centerY
    return {
      top: useTop ? Math.max(0, Math.round(y)) : 0,
      right: !useLeft ? Math.max(0, Math.round(parentRect.width - x)) : 0,
      bottom: !useTop ? Math.max(0, Math.round(parentRect.height - y)) : 0,
      left: useLeft ? Math.max(0, Math.round(x)) : 0,
    }
  }

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: ['element', 'media', 'placed-element'],
    drop: (item, monitor) => {
      if (!monitor.didDrop()) {
        const clientOffset = monitor.getClientOffset()
        const rect = sectionRef.current?.getBoundingClientRect()
        if (clientOffset && rect) {
          const margin = computeMargin(rect, clientOffset)

          // 移动已存在元素
          if (item.elementId) {
            useEditorStore.getState().updateElement(item.elementId, { margin })
            return
          }

          // 新增元素
          const position = { x: 0, y: 0 }
          const elementType = item.elementType
            ? item.elementType
            : (item.mediaType === 'video' ? 'video' : 'image')

          addElement(elementType, position, { sectionId: section.id, margin })
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  }))

  const hasContent = (section.elements && section.elements.length > 0)
    || (section.containers && section.containers.length > 0)
    || (section.stacks && section.stacks.length > 0)

  const isSelected = selectedSection === section.id

  return (
    <div className="relative group">
      <div
        ref={(node) => {
          sectionRef.current = node
          drop(node)
        }}
        className={`
          relative overflow-hidden transition-all duration-200
          ${isOver && canDrop ? 'ring-2 ring-green-400 bg-green-50' : ''}
          ${isSelected ? 'ring-2 ring-blue-400' : ''}
          ${!hasContent ? 'hover:ring-1 hover:ring-blue-200' : ''}
        `}
        style={{ height: section.height, minHeight: '120px' }}
        data-section-id={section.id}
        onClick={(e) => {
          // 只有点击的是section本身（不是子元素）时才选中section
          if (e.target === e.currentTarget) {
            e.stopPropagation()
            selectSection(section.id)
          }
        }}
      >
        {hasContent ? (
          <>
            {/* 分层架构：按照技术文档的设计理念 - 所有层级平级 */}
            <div className="relative h-full">
              {/* 背景层 - z-index: 0 */}
              <div
                className="absolute inset-0 bg-white"
                style={{ zIndex: 0 }}
                data-layer="background-layer"
              >
                {/* 可以添加网格背景等 */}
              </div>

              {/* 元素层 - z-index: 1 */}
              <div
                className="absolute inset-0"
                style={{ zIndex: 1 }}
                data-layer="element-layer"
              >
                {/* 容器 */}
                {section.containers?.map((container) => (
                  <Container key={container.id} container={container} sectionId={section.id} />
                ))}

                {/* 直接在区段中的元素 */}
                {section.elements?.map((element) => (
                  <CanvasElement key={element.id} element={element} />
                ))}
              </div>

              {/* 辅助线层 - z-index: 5 */}
              <div
                className="absolute inset-0 pointer-events-none"
                style={{ zIndex: 5 }}
                data-layer="guide-layer"
                id={`guide-layer-host-${section.id}`}
              />

              {/* 选中框层 - z-index: 10 */}
              <div
                className="absolute inset-0 pointer-events-none"
                style={{ zIndex: 10 }}
                data-layer="selection-layer"
                id={`selection-overlay-host-${section.id}`}
              />

              {/* 工具层 - z-index: 15 */}
              <div
                className="absolute inset-0 pointer-events-none"
                style={{ zIndex: 15 }}
                data-layer="tool-layer"
                id={`tool-layer-host-${section.id}`}
              />
            </div>
          </>
        ) : (
          <>
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center text-gray-400">
                <div className="text-sm mb-2">空白区段</div>
                <div className="text-xs">拖拽组件到这里</div>
              </div>
            </div>
            <button
              className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity px-3 py-1 bg-blue-50/70 hover:bg-blue-100/80 rounded border border-blue-200 pointer-events-auto"
              onClick={(e) => {
                e.stopPropagation()
                const rect = sectionRef.current?.getBoundingClientRect()
                if (rect) {
                  const centerMargin = {
                    top: Math.round(rect.height / 2 - 20),
                    left: Math.round(rect.width / 2 - 60),
                    right: 0,
                    bottom: 0,
                  }
                  addElement('text', { x: 0, y: 0 }, { sectionId: section.id, margin: centerMargin })
                } else {
                  addElement('text', { x: 0, y: 0 }, { sectionId: section.id, margin: { top: 16, left: 16, right: 0, bottom: 0 } })
                }
              }}
            >
              + 添加元素
            </button>
          </>
        )}
      </div>

      {/* 区段底部的新增按钮 */}
      <div className="absolute -bottom-3 left-1/2 -translate-x-1/2 z-10">
        <button
          className="px-3 py-1 text-xs bg-white border border-gray-300 rounded shadow hover:bg-gray-50 hover:border-gray-400 transition-colors"
          onClick={(e) => {
            e.stopPropagation()
            addSection(section.id)
          }}
        >
          新增区段
        </button>
      </div>
    </div>
  )
}

export default Section

