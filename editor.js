// 可视化编辑器核心功能
class VisualEditor {
    constructor() {
        this.currentBreakpoint = 'desktop';
        this.zoomLevel = 100;
        this.selectedElement = null;
        this.elements = [];
        this.history = [];
        this.historyIndex = -1;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initCanvas();
        this.setupDragAndDrop();
        this.setupPanels();
    }

    setupEventListeners() {
        // 断点切换
        document.querySelectorAll('.breakpoint-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchBreakpoint(e.target.closest('.breakpoint-btn'));
            });
        });

        // 缩放控制
        document.querySelectorAll('.zoom-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const isZoomIn = e.target.textContent === '+';
                this.adjustZoom(isZoomIn);
            });
        });

        // 侧边栏按钮
        document.querySelectorAll('.sidebar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.togglePanel(e.target.closest('.sidebar-btn'));
            });
        });

        // 预览和发布
        document.querySelector('.btn-secondary').addEventListener('click', () => {
            this.preview();
        });

        document.querySelector('.btn-primary').addEventListener('click', () => {
            this.publish();
        });

        // 属性面板输入
        document.querySelectorAll('.property-input').forEach(input => {
            input.addEventListener('input', (e) => {
                this.updateElementProperty(e.target);
            });
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    initCanvas() {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
        
        // 初始化画布内容
        canvasDoc.open();
        canvasDoc.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    * { margin: 0; padding: 0; box-sizing: border-box; }
                    body { 
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        min-height: 100vh;
                        background: #fff;
                    }
                    .editable-element {
                        position: relative;
                        cursor: pointer;
                        transition: all 0.2s;
                    }
                    .editable-element:hover {
                        outline: 2px dashed #4CAF50;
                    }
                    .editable-element.selected {
                        outline: 2px solid #4CAF50;
                    }
                    .element-controls {
                        position: absolute;
                        top: -30px;
                        right: 0;
                        background: #4CAF50;
                        color: white;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        display: none;
                    }
                    .editable-element.selected .element-controls {
                        display: block;
                    }
                    .default-content {
                        padding: 40px;
                        text-align: center;
                        color: #666;
                    }
                </style>
            </head>
            <body>
                <div class="default-content">
                    <h1>欢迎使用可视化编辑器</h1>
                    <p>从左侧拖拽元素到这里开始创建您的页面</p>
                </div>
            </body>
            </html>
        `);
        canvasDoc.close();

        // 设置画布事件监听
        canvasDoc.addEventListener('click', (e) => {
            this.selectElement(e.target);
        });

        canvasDoc.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleCanvasDrop(e);
        });

        canvasDoc.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
    }

    setupDragAndDrop() {
        document.querySelectorAll('.element-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                const elementType = e.target.closest('.element-item').dataset.element;
                e.dataTransfer.setData('text/plain', elementType);
            });
        });
    }

    setupPanels() {
        // 默认显示元素面板
        this.showPanel('elements');

        // 设置页面管理
        this.setupPageManagement();

        // 设置主题管理
        this.setupThemeManagement();

        // 设置媒体管理
        this.setupMediaManagement();

        // 设置图层管理
        this.setupLayerManagement();
    }

    setupPageManagement() {
        // 添加页面按钮
        document.querySelector('.add-page-btn').addEventListener('click', () => {
            this.addNewPage();
        });

        // 页面切换
        document.querySelectorAll('.page-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('page-btn')) {
                    this.switchPage(item);
                }
            });
        });
    }

    setupThemeManagement() {
        // 颜色选择
        document.querySelectorAll('.color-item').forEach(item => {
            item.addEventListener('click', () => {
                this.applyThemeColor(item.dataset.color);
            });
        });

        // 主题预设
        document.querySelectorAll('.theme-preset').forEach(preset => {
            preset.addEventListener('click', () => {
                this.applyThemePreset(preset.dataset.theme);
            });
        });

        // 字体选择
        document.querySelector('.theme-select').addEventListener('change', (e) => {
            this.applyFont(e.target.value);
        });
    }

    setupMediaManagement() {
        // 文件上传
        document.getElementById('media-upload').addEventListener('change', (e) => {
            this.handleMediaUpload(e.files);
        });

        // 媒体项拖拽
        document.querySelectorAll('.media-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', 'media');
                e.dataTransfer.setData('media-src', item.dataset.src);
            });
        });
    }

    setupLayerManagement() {
        this.updateLayersList();
    }

    switchBreakpoint(btn) {
        document.querySelectorAll('.breakpoint-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        const canvas = document.querySelector('.canvas-frame');
        const container = document.querySelector('.canvas-container');
        
        if (btn.dataset.tooltip === '平板') {
            canvas.style.width = '768px';
            this.currentBreakpoint = 'tablet';
        } else if (btn.dataset.tooltip === '手机') {
            canvas.style.width = '375px';
            this.currentBreakpoint = 'mobile';
        } else {
            canvas.style.width = '1200px';
            this.currentBreakpoint = 'desktop';
        }
        
        this.updateCanvasScale();
    }

    adjustZoom(isZoomIn) {
        const step = 10;
        if (isZoomIn && this.zoomLevel < 200) {
            this.zoomLevel += step;
        } else if (!isZoomIn && this.zoomLevel > 50) {
            this.zoomLevel -= step;
        }
        
        document.querySelector('.zoom-control span').textContent = this.zoomLevel + '%';
        this.updateCanvasScale();
    }

    updateCanvasScale() {
        const container = document.querySelector('.canvas-container');
        const scale = this.zoomLevel / 100;
        container.style.transform = `scale(${scale})`;
        container.style.transformOrigin = 'top center';
    }

    togglePanel(btn) {
        document.querySelectorAll('.sidebar-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        const panelName = btn.dataset.panel;
        this.showPanel(panelName);
    }

    showPanel(panelName) {
        document.querySelectorAll('.left-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        const targetPanel = document.getElementById(`${panelName}-panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
    }

    addNewPage() {
        const pageName = prompt('请输入页面名称:');
        if (pageName) {
            const pagesList = document.querySelector('.pages-list');
            const pageItem = document.createElement('div');
            pageItem.className = 'page-item';
            pageItem.innerHTML = `
                <span class="page-name">${pageName}</span>
                <div class="page-controls">
                    <button class="page-btn">⚙️</button>
                    <button class="page-btn">🗑️</button>
                </div>
            `;
            pagesList.appendChild(pageItem);

            // 添加事件监听
            pageItem.addEventListener('click', (e) => {
                if (!e.target.classList.contains('page-btn')) {
                    this.switchPage(pageItem);
                }
            });
        }
    }

    switchPage(pageItem) {
        document.querySelectorAll('.page-item').forEach(item => {
            item.classList.remove('active');
        });
        pageItem.classList.add('active');

        // 这里可以实现页面切换逻辑
        console.log('切换到页面:', pageItem.querySelector('.page-name').textContent);
    }

    applyThemeColor(color) {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;

        // 应用主题色到页面
        const style = canvasDoc.createElement('style');
        style.textContent = `
            :root {
                --theme-color: ${color};
            }
            .editable-element button {
                background-color: ${color} !important;
            }
            .editable-element.selected {
                outline-color: ${color} !important;
            }
        `;
        canvasDoc.head.appendChild(style);
    }

    applyThemePreset(theme) {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;

        let themeStyles = '';
        switch (theme) {
            case 'modern':
                themeStyles = `
                    body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                    .editable-element { border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                `;
                break;
            case 'classic':
                themeStyles = `
                    body { background: #f5f5f5; font-family: serif; }
                    .editable-element { border: 1px solid #ddd; }
                `;
                break;
            case 'minimal':
                themeStyles = `
                    body { background: white; }
                    .editable-element { border: none; box-shadow: none; }
                `;
                break;
        }

        const style = canvasDoc.createElement('style');
        style.textContent = themeStyles;
        canvasDoc.head.appendChild(style);
    }

    applyFont(fontFamily) {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;

        const style = canvasDoc.createElement('style');
        style.textContent = `
            body { font-family: ${fontFamily}, sans-serif; }
        `;
        canvasDoc.head.appendChild(style);
    }

    handleMediaUpload(files) {
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.addMediaItem(e.target.result, file.name);
            };
            reader.readAsDataURL(file);
        });
    }

    addMediaItem(src, name) {
        const mediaGrid = document.querySelector('.media-grid');
        const mediaItem = document.createElement('div');
        mediaItem.className = 'media-item';
        mediaItem.draggable = true;
        mediaItem.dataset.src = src;

        const isVideo = name.toLowerCase().includes('.mp4') || name.toLowerCase().includes('.webm');

        mediaItem.innerHTML = `
            ${isVideo ?
                `<video style="width: 100%; height: 80px; object-fit: cover;"><source src="${src}"></video>` :
                `<img src="${src}" alt="${name}" style="width: 100%; height: 80px; object-fit: cover;">`
            }
            <div class="media-name">${name}</div>
        `;

        // 添加拖拽事件
        mediaItem.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', 'media');
            e.dataTransfer.setData('media-src', src);
            e.dataTransfer.setData('media-type', isVideo ? 'video' : 'image');
        });

        mediaGrid.appendChild(mediaItem);
    }

    updateLayersList() {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
        const layersList = document.getElementById('layers-list');

        // 清空现有图层
        layersList.innerHTML = '';

        // 添加根元素
        const rootLayer = document.createElement('div');
        rootLayer.className = 'layer-item';
        rootLayer.innerHTML = `
            <span class="layer-name">页面根元素</span>
            <div class="layer-controls">
                <button class="layer-btn">👁️</button>
                <button class="layer-btn">🔒</button>
            </div>
        `;
        layersList.appendChild(rootLayer);

        // 添加所有可编辑元素
        const elements = canvasDoc.querySelectorAll('.editable-element');
        elements.forEach((element, index) => {
            const layerItem = document.createElement('div');
            layerItem.className = 'layer-item';
            layerItem.innerHTML = `
                <span class="layer-name">${element.dataset.elementType || '元素'} ${index + 1}</span>
                <div class="layer-controls">
                    <button class="layer-btn">👁️</button>
                    <button class="layer-btn">🔒</button>
                </div>
            `;

            // 点击图层选中元素
            layerItem.addEventListener('click', () => {
                this.selectElement(element);
            });

            layersList.appendChild(layerItem);
        });
    }

    handleCanvasDrop(e) {
        const elementType = e.dataTransfer.getData('text/plain');
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;

        // 移除默认内容
        const defaultContent = canvasDoc.querySelector('.default-content');
        if (defaultContent) {
            defaultContent.remove();
        }

        let element;

        if (elementType === 'media') {
            // 处理媒体文件拖拽
            const mediaSrc = e.dataTransfer.getData('media-src');
            const mediaType = e.dataTransfer.getData('media-type');
            element = this.createElement(mediaType === 'video' ? 'video' : 'image', canvasDoc, mediaSrc);
        } else {
            // 处理普通元素拖拽
            element = this.createElement(elementType, canvasDoc);
        }

        // 计算插入位置
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        element.style.position = 'absolute';
        element.style.left = x + 'px';
        element.style.top = y + 'px';

        canvasDoc.body.appendChild(element);
        this.selectElement(element);

        // 更新图层列表
        this.updateLayersList();

        // 保存到历史记录
        this.saveToHistory();
    }

    createElement(type, doc, mediaSrc = null) {
        const element = doc.createElement('div');
        element.className = 'editable-element';
        element.dataset.elementType = type;

        const controls = doc.createElement('div');
        controls.className = 'element-controls';
        controls.innerHTML = `${type} <span onclick="this.parentElement.parentElement.remove(); window.parent.editor.updateLayersList();">×</span>`;
        element.appendChild(controls);

        switch (type) {
            case 'text':
                element.innerHTML += '<p contenteditable="true">这是一段文本，点击编辑</p>';
                element.style.padding = '10px';
                break;
            case 'image':
                const imgSrc = mediaSrc || 'https://via.placeholder.com/200x150';
                element.innerHTML += `<img src="${imgSrc}" alt="图片" style="max-width: 100%;">`;
                break;
            case 'button':
                element.innerHTML += '<button style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">按钮</button>';
                break;
            case 'container':
                element.innerHTML += '<div style="min-height: 100px; background: #f5f5f5; border: 2px dashed #ccc; padding: 20px; text-align: center;">容器 - 拖拽元素到这里</div>';
                break;
            case 'form':
                element.innerHTML += `
                    <form style="padding: 20px; border: 1px solid #ddd; border-radius: 4px;">
                        <input type="text" placeholder="姓名" style="width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <input type="email" placeholder="邮箱" style="width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <button type="submit" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px;">提交</button>
                    </form>
                `;
                break;
            case 'video':
                const videoSrc = mediaSrc || '';
                element.innerHTML += `<video controls style="width: 100%; max-width: 400px;"><source src="${videoSrc}" type="video/mp4">您的浏览器不支持视频标签。</video>`;
                break;
            case 'gallery':
                element.innerHTML += `
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; padding: 20px;">
                        <img src="https://via.placeholder.com/150" style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;">
                        <img src="https://via.placeholder.com/150/0000FF" style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;">
                        <img src="https://via.placeholder.com/150/FF0000" style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;">
                    </div>
                `;
                break;
            case 'map':
                element.innerHTML += `
                    <div style="width: 100%; height: 300px; background: #e0e0e0; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                        <div style="text-align: center; color: #666;">
                            <div style="font-size: 24px; margin-bottom: 10px;">🗺️</div>
                            <div>地图组件</div>
                            <div style="font-size: 12px;">点击配置地图设置</div>
                        </div>
                    </div>
                `;
                break;
        }

        return element;
    }

    selectElement(element) {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
        
        // 移除之前的选中状态
        canvasDoc.querySelectorAll('.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        if (element.classList && element.classList.contains('editable-element')) {
            element.classList.add('selected');
            this.selectedElement = element;
            this.updatePropertiesPanel(element);
        }
    }

    updatePropertiesPanel(element) {
        const style = window.getComputedStyle(element);
        
        // 更新属性面板的值
        const inputs = document.querySelectorAll('.property-input');
        inputs[0].value = style.width || 'auto';
        inputs[1].value = style.height || 'auto';
        inputs[2].value = this.rgbToHex(style.backgroundColor) || '#ffffff';
        inputs[3].value = style.margin || '0px';
        inputs[4].value = style.padding || '0px';
    }

    updateElementProperty(input) {
        if (!this.selectedElement) return;
        
        const property = input.parentElement.querySelector('.property-label').textContent;
        const value = input.value;
        
        switch (property) {
            case '宽度':
                this.selectedElement.style.width = value;
                break;
            case '高度':
                this.selectedElement.style.height = value;
                break;
            case '背景颜色':
                this.selectedElement.style.backgroundColor = value;
                break;
            case '边距':
                this.selectedElement.style.margin = value;
                break;
            case '内边距':
                this.selectedElement.style.padding = value;
                break;
        }
        
        this.saveToHistory();
    }

    rgbToHex(rgb) {
        if (!rgb || rgb === 'rgba(0, 0, 0, 0)') return '#ffffff';
        const result = rgb.match(/\d+/g);
        if (!result) return '#ffffff';
        return '#' + result.slice(0, 3).map(x => {
            const hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
    }

    handleKeyboard(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'z':
                    e.preventDefault();
                    this.undo();
                    break;
                case 'y':
                    e.preventDefault();
                    this.redo();
                    break;
                case 's':
                    e.preventDefault();
                    this.save();
                    break;
            }
        }
        
        if (e.key === 'Delete' && this.selectedElement) {
            this.selectedElement.remove();
            this.selectedElement = null;
            this.saveToHistory();
        }
    }

    saveToHistory() {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
        const state = canvasDoc.body.innerHTML;
        
        // 移除当前位置之后的历史记录
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push(state);
        this.historyIndex++;
        
        // 限制历史记录数量
        if (this.history.length > 50) {
            this.history.shift();
            this.historyIndex--;
        }
    }

    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.restoreFromHistory();
        }
    }

    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.restoreFromHistory();
        }
    }

    restoreFromHistory() {
        const canvas = document.getElementById('canvas-frame');
        const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
        canvasDoc.body.innerHTML = this.history[this.historyIndex];
    }

    preview() {
        const canvas = document.getElementById('canvas-frame');
        const newWindow = window.open('', '_blank');
        newWindow.document.write(canvas.contentDocument.documentElement.outerHTML);
        newWindow.document.close();
    }

    publish() {
        const canvas = document.getElementById('canvas-frame');
        const html = canvas.contentDocument.documentElement.outerHTML;
        
        // 这里可以实现发布逻辑，比如发送到服务器
        console.log('发布内容:', html);
        alert('页面发布成功！');
    }

    save() {
        const canvas = document.getElementById('canvas-frame');
        const html = canvas.contentDocument.documentElement.outerHTML;
        
        // 保存到本地存储
        localStorage.setItem('visual-editor-content', html);
        
        // 更新自动保存指示器
        const indicator = document.querySelector('.autosave-indicator span');
        indicator.textContent = '已保存';
        setTimeout(() => {
            indicator.textContent = '自动保存已启用';
        }, 2000);
    }

    load() {
        const saved = localStorage.getItem('visual-editor-content');
        if (saved) {
            const canvas = document.getElementById('canvas-frame');
            const canvasDoc = canvas.contentDocument || canvas.contentWindow.document;
            canvasDoc.open();
            canvasDoc.write(saved);
            canvasDoc.close();
        }
    }
}

// 初始化编辑器
document.addEventListener('DOMContentLoaded', () => {
    const editor = new VisualEditor();

    // 将编辑器实例设为全局变量，供画布内元素使用
    window.editor = editor;

    // 自动保存
    setInterval(() => {
        editor.save();
    }, 30000); // 每30秒自动保存
});
