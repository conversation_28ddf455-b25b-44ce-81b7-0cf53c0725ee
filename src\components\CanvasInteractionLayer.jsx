import React, { useRef, useEffect, useState } from 'react'
import { useEditorStore } from '../store/editorStore'

const CanvasInteractionLayer = () => {
  const {
    selectElement,
    isPreviewMode,
  } = useEditorStore()
  
  const layerRef = useRef(null)
  const [isDragActive, setIsDragActive] = useState(false)

  // 处理点击事件
  const handleClick = (e) => {
    if (isPreviewMode) return

    // 检查是否正在拖拽（从元素面板拖拽）
    if (e.target.closest('[data-dnd-dragging]')) {
      return // 不处理拖拽时的点击
    }

    // 查找最近的元素
    const elementNode = e.target.closest('[data-element-id]')
    
    if (elementNode) {
      // 点击了元素，选中它
      const elementId = elementNode.getAttribute('data-element-id')
      selectElement(elementId)
    } else {
      // 点击了空白区域，清除选择
      selectElement(null)
    }
  }

  // 阻止右键菜单
  const handleContextMenu = (e) => {
    if (!isPreviewMode) {
      e.preventDefault()
    }
  }

  // 监听拖拽状态
  useEffect(() => {
    const handleGlobalDragStart = () => {
      setIsDragActive(true)
    }
    const handleGlobalDragEnd = () => {
      setIsDragActive(false)
    }
    const handleGlobalDrop = () => {
      setIsDragActive(false)
    }

    window.addEventListener('dragstart', handleGlobalDragStart)
    window.addEventListener('dragend', handleGlobalDragEnd)
    window.addEventListener('drop', handleGlobalDrop)

    return () => {
      window.removeEventListener('dragstart', handleGlobalDragStart)
      window.removeEventListener('dragend', handleGlobalDragEnd)
      window.removeEventListener('drop', handleGlobalDrop)
    }
  }, [])

  // console.log('CanvasInteractionLayer render:', { isDragActive, pointerEvents: isDragActive ? 'none' : 'auto' })

  return (
    <div
      ref={layerRef}
      className="absolute inset-0 z-10"
      style={{
        backgroundColor: 'transparent',
        cursor: isPreviewMode ? 'default' : 'pointer',
        // 动态调整 pointerEvents：拖拽时让路，正常状态下允许点击选择
        pointerEvents: isDragActive ? 'none' : 'auto',
      }}
      onClick={handleClick}
      onDragEnter={() => setIsDragActive(true)}
      onDragOver={() => setIsDragActive(true)}
      onDragLeave={() => setIsDragActive(false)}
      onContextMenu={handleContextMenu}
    />
  )
}

export default CanvasInteractionLayer
