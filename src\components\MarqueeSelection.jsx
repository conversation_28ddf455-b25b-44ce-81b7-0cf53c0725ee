import React, { useRef, useCallback } from 'react'
import { useEditorStore } from '../store/editorStore'

const MarqueeSelection = ({ children }) => {
  const {
    marqueeActive,
    marqueeRect,
    beginMarquee,
    updateMarqueeRect,
    endMarquee,
    clearMarquee,
    setSelectedElements,
    currentSections,
    isDraggingElement
  } = useEditorStore()

  const containerRef = useRef(null)
  const startPointRef = useRef({ x: 0, y: 0 })
  const isDraggingRef = useRef(false)

  // 开始框选
  const handleMouseDown = useCallback((e) => {
    // 如果正在拖拽元素，不启动框选
    if (isDraggingElement) return

    // 只在点击空白区域时开始框选（不是元素或其他组件）
    if (e.target !== e.currentTarget) return

    // 不是左键点击则返回
    if (e.button !== 0) return

    // 检查是否点击在元素上
    const clickedElement = e.target.closest('[data-element-id]')
    if (clickedElement) return

    e.preventDefault()
    e.stopPropagation()

    const container = containerRef.current
    if (!container) return

    const rect = container.getBoundingClientRect()
    const startX = e.clientX - rect.left
    const startY = e.clientY - rect.top

    startPointRef.current = { x: startX, y: startY }
    isDraggingRef.current = true

    // 开始框选
    beginMarquee({
      left: startX,
      top: startY,
      width: 0,
      height: 0
    })

    // 禁用文本选择
    document.body.style.userSelect = 'none'
    document.body.classList.add('marquee-active')

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [beginMarquee])

  // 更新框选区域
  const handleMouseMove = useCallback((e) => {
    if (!isDraggingRef.current || !containerRef.current) return

    const container = containerRef.current
    const rect = container.getBoundingClientRect()
    const currentX = e.clientX - rect.left
    const currentY = e.clientY - rect.top

    const startX = startPointRef.current.x
    const startY = startPointRef.current.y

    // 计算框选矩形
    const left = Math.min(startX, currentX)
    const top = Math.min(startY, currentY)
    const width = Math.abs(currentX - startX)
    const height = Math.abs(currentY - startY)

    updateMarqueeRect({
      left,
      top,
      width,
      height
    })
  }, [updateMarqueeRect])

  // 结束框选
  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) return

    isDraggingRef.current = false

    // 恢复文本选择
    document.body.style.userSelect = ''
    document.body.classList.remove('marquee-active')

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)

    // 计算被框选的元素
    if (marqueeRect && (marqueeRect.width > 5 || marqueeRect.height > 5)) {
      const selectedIds = getElementsInMarquee()
      if (selectedIds.length > 0) {
        // 设置多选元素，这会自动清除单个元素选择
        setSelectedElements(selectedIds)
      } else {
        // 如果没有框选到元素，清除所有选择
        setSelectedElements([])
      }
    } else {
      // 如果框选区域太小，清除选择
      setSelectedElements([])
    }

    // 结束框选
    endMarquee()
    
    // 延迟清除框选矩形，让用户能看到选择结果
    setTimeout(() => {
      clearMarquee()
    }, 100)
  }, [marqueeRect, endMarquee, clearMarquee, setSelectedElements])

  // 获取框选区域内的元素
  const getElementsInMarquee = useCallback(() => {
    if (!marqueeRect || !containerRef.current) return []

    // 直接使用相对坐标进行比较，因为元素也是相对于同一个容器定位的
    const marqueeArea = {
      left: marqueeRect.left,
      top: marqueeRect.top,
      right: marqueeRect.left + marqueeRect.width,
      bottom: marqueeRect.top + marqueeRect.height
    }

    const selectedIds = []

    // 查找所有区段的所有元素


    currentSections.forEach(section => {
      const elements = section.elements || []


      elements.forEach(element => {
      const elementNode = document.querySelector(`[data-element-id="${element.id}"]`)
      if (!elementNode) {

        return
      }

      // 获取元素相对于容器的位置
      const container = containerRef.current
      const containerRect = container.getBoundingClientRect()
      const elementRect = elementNode.getBoundingClientRect()

      // 转换为相对坐标
      const elementRelative = {
        left: elementRect.left - containerRect.left,
        top: elementRect.top - containerRect.top,
        right: elementRect.right - containerRect.left,
        bottom: elementRect.bottom - containerRect.top
      }

      // 检查元素是否与框选区域相交
      const isIntersecting = !(
        elementRelative.right < marqueeArea.left ||
        elementRelative.left > marqueeArea.right ||
        elementRelative.bottom < marqueeArea.top ||
        elementRelative.top > marqueeArea.bottom
      )



        if (isIntersecting) {
          selectedIds.push(element.id)
        }
      })
    })

    return selectedIds
  }, [marqueeRect, currentSections])

  return (
    <div
      ref={containerRef}
      className="absolute inset-0"
      onMouseDown={handleMouseDown}
      style={{ zIndex: 1 }}
    >
      {children}
      
      {/* 框选矩形 */}
      {marqueeActive && marqueeRect && marqueeRect.width > 0 && marqueeRect.height > 0 && (
        <div
          className="absolute pointer-events-none marquee-rect"
          style={{
            left: marqueeRect.left,
            top: marqueeRect.top,
            width: marqueeRect.width,
            height: marqueeRect.height,
            border: '1px dashed #116dff',
            backgroundColor: 'rgba(17, 109, 255, 0.1)',
            zIndex: 1000,
            borderRadius: '2px'
          }}
        />
      )}
    </div>
  )
}

export default MarqueeSelection
