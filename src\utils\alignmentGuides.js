/**
 * 对齐辅助线管理器
 * 按照技术文档的设计理念实现
 */

export class AlignmentGuides {
  constructor(hostElement) {
    this.hostElement = hostElement
    this.guides = {
      vertical: [],
      horizontal: []
    }
    this.tolerance = 5 // 对齐容差
  }

  /**
   * 显示对齐辅助线
   * @param {Object} draggedElement - 被拖拽的元素
   * @param {Array} otherElements - 其他元素列表
   */
  showGuides(draggedElement, otherElements) {
    this.clearGuides()

    if (!draggedElement || !otherElements) return

    const draggedBounds = this.getElementBounds(draggedElement)
    
    otherElements.forEach(element => {
      const bounds = this.getElementBounds(element)
      this.checkAlignment(draggedBounds, bounds)
    })
  }

  /**
   * 获取元素边界框
   */
  getElementBounds(element) {
    const margin = element.margin || { top: 0, left: 0 }
    const size = element.size || { width: 'auto', height: 'auto' }
    
    // 获取实际尺寸
    const width = size.width === 'auto' ? 100 : parseInt(size.width)
    const height = size.height === 'auto' ? 40 : parseInt(size.height)
    
    return {
      x: margin.left || 0,
      y: margin.top || 0,
      width,
      height
    }
  }

  /**
   * 检查对齐关系
   */
  checkAlignment(draggedBounds, targetBounds) {
    // 垂直对齐检查
    this.checkVerticalAlignment(draggedBounds, targetBounds)
    
    // 水平对齐检查
    this.checkHorizontalAlignment(draggedBounds, targetBounds)
  }

  /**
   * 检查垂直对齐
   */
  checkVerticalAlignment(draggedBounds, targetBounds) {
    const draggedCenter = draggedBounds.x + draggedBounds.width / 2
    const targetCenter = targetBounds.x + targetBounds.width / 2

    // 中心对齐
    if (Math.abs(draggedCenter - targetCenter) < this.tolerance) {
      this.addVerticalGuide(targetCenter)
    }

    // 左边对齐
    if (Math.abs(draggedBounds.x - targetBounds.x) < this.tolerance) {
      this.addVerticalGuide(targetBounds.x)
    }

    // 右边对齐
    if (Math.abs(
      draggedBounds.x + draggedBounds.width -
      targetBounds.x - targetBounds.width
    ) < this.tolerance) {
      this.addVerticalGuide(targetBounds.x + targetBounds.width)
    }
  }

  /**
   * 检查水平对齐
   */
  checkHorizontalAlignment(draggedBounds, targetBounds) {
    const draggedCenter = draggedBounds.y + draggedBounds.height / 2
    const targetCenter = targetBounds.y + targetBounds.height / 2

    // 中心对齐
    if (Math.abs(draggedCenter - targetCenter) < this.tolerance) {
      this.addHorizontalGuide(targetCenter)
    }

    // 顶部对齐
    if (Math.abs(draggedBounds.y - targetBounds.y) < this.tolerance) {
      this.addHorizontalGuide(targetBounds.y)
    }

    // 底部对齐
    if (Math.abs(
      draggedBounds.y + draggedBounds.height -
      targetBounds.y - targetBounds.height
    ) < this.tolerance) {
      this.addHorizontalGuide(targetBounds.y + targetBounds.height)
    }
  }

  /**
   * 添加垂直辅助线
   */
  addVerticalGuide(x) {
    const guide = document.createElement('div')
    guide.className = 'alignment-guide vertical'
    guide.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: 0;
      width: 1px;
      height: 100%;
      background: #ff6b6b;
      pointer-events: none;
      z-index: 1000;
    `

    this.hostElement.appendChild(guide)
    this.guides.vertical.push(guide)
  }

  /**
   * 添加水平辅助线
   */
  addHorizontalGuide(y) {
    const guide = document.createElement('div')
    guide.className = 'alignment-guide horizontal'
    guide.style.cssText = `
      position: absolute;
      left: 0;
      top: ${y}px;
      width: 100%;
      height: 1px;
      background: #ff6b6b;
      pointer-events: none;
      z-index: 1000;
    `

    this.hostElement.appendChild(guide)
    this.guides.horizontal.push(guide)
  }

  /**
   * 清除所有辅助线
   */
  clearGuides() {
    [...this.guides.vertical, ...this.guides.horizontal]
      .forEach(guide => {
        if (guide.parentNode) {
          guide.parentNode.removeChild(guide)
        }
      })

    this.guides.vertical = []
    this.guides.horizontal = []
  }

  /**
   * 计算吸附位置
   */
  calculateSnapPosition(elementBounds) {
    const result = {
      position: { top: elementBounds.y, left: elementBounds.x },
      activeGuides: []
    }

    // 检查垂直吸附
    this.guides.vertical.forEach(guide => {
      const guideX = parseInt(guide.style.left)
      const elementCenter = elementBounds.x + elementBounds.width / 2
      
      if (Math.abs(elementCenter - guideX) < this.tolerance) {
        result.position.left = guideX - elementBounds.width / 2
        result.activeGuides.push(guide)
      } else if (Math.abs(elementBounds.x - guideX) < this.tolerance) {
        result.position.left = guideX
        result.activeGuides.push(guide)
      }
    })

    // 检查水平吸附
    this.guides.horizontal.forEach(guide => {
      const guideY = parseInt(guide.style.top)
      const elementCenter = elementBounds.y + elementBounds.height / 2
      
      if (Math.abs(elementCenter - guideY) < this.tolerance) {
        result.position.top = guideY - elementBounds.height / 2
        result.activeGuides.push(guide)
      } else if (Math.abs(elementBounds.y - guideY) < this.tolerance) {
        result.position.top = guideY
        result.activeGuides.push(guide)
      }
    })

    return result
  }

  /**
   * 销毁辅助线管理器
   */
  destroy() {
    this.clearGuides()
    this.hostElement = null
  }
}

export default AlignmentGuides
