import React, { useRef } from 'react'
import { useDrop } from 'react-dnd'
import { useEditorStore, ELEMENT_TYPES } from '../store/editorStore'
import CanvasElement from './CanvasElement'

/**
 * 容器组件 - 实现区段 > 容器/堆叠 > 元素的层级结构
 * 容器可以包含多个元素，支持不同的布局模式
 */
const Container = ({ container, sectionId }) => {
  const { updateContainer, selectElement, selectedElements } = useEditorStore()
  const containerRef = useRef(null)

  // 容器类型
  const CONTAINER_TYPES = {
    FLEX: 'flex',      // 弹性布局容器
    GRID: 'grid',      // 网格布局容器
    STACK: 'stack',    // 堆叠容器
    FREE: 'free'       // 自由定位容器
  }

  // 容器接受元素拖拽
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: ['element', 'media'],
    drop: (item, monitor) => {
      const clientOffset = monitor.getClientOffset()
      const rect = containerRef.current?.getBoundingClientRect()
      if (!clientOffset || !rect) return

      const x = clientOffset.x - rect.left
      const y = clientOffset.y - rect.top

      // 根据容器类型决定元素的定位方式
      let elementData = {
        id: Math.random().toString(36).slice(2, 9),
        type: item.elementType ? item.elementType : (item.mediaType === 'video' ? ELEMENT_TYPES.VIDEO : ELEMENT_TYPES.IMAGE),
        content: item.elementType === ELEMENT_TYPES.TEXT ? '新文本' : undefined,
        src: item.elementType === ELEMENT_TYPES.IMAGE ? 'https://via.placeholder.com/150' : undefined,
        size: { width: 'auto', height: 'auto' },
        style: { backgroundColor: 'transparent' },
      }

      switch (container.type) {
        case CONTAINER_TYPES.FREE:
          // 自由定位：使用 margin
          elementData.margin = { top: Math.round(y), left: Math.round(x), right: 0, bottom: 0 }
          break
        case CONTAINER_TYPES.FLEX:
          // 弹性布局：添加到末尾，不需要定位
          elementData.flexOrder = (container.elements?.length || 0) + 1
          break
        case CONTAINER_TYPES.GRID:
          // 网格布局：计算网格位置
          const cols = container.gridCols || 3
          const index = container.elements?.length || 0
          elementData.gridRow = Math.floor(index / cols) + 1
          elementData.gridCol = (index % cols) + 1
          break
        case CONTAINER_TYPES.STACK:
          // 堆叠：使用 z-index
          elementData.zIndex = (container.elements?.length || 0) + 1
          elementData.margin = { top: Math.round(y), left: Math.round(x), right: 0, bottom: 0 }
          break
        default:
          elementData.margin = { top: Math.round(y), left: Math.round(x), right: 0, bottom: 0 }
      }

      // 添加元素到容器
      const updatedElements = [...(container.elements || []), elementData]
      updateContainer(sectionId, container.id, { elements: updatedElements })
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }))

  // 容器样式
  const getContainerStyle = () => {
    const baseStyle = {
      position: 'relative',
      marginTop: `${container.margin?.top || 0}px`,
      marginLeft: `${container.margin?.left || 0}px`,
      marginRight: `${container.margin?.right || 0}px`,
      marginBottom: `${container.margin?.bottom || 0}px`,
      width: container.size?.width || 'auto',
      height: container.size?.height || 'auto',
      minHeight: '100px',
      border: isOver && canDrop ? '2px dashed #22c55e' : '2px dashed #d1d5db',
      backgroundColor: isOver && canDrop ? '#f0fdf4' : (container.style?.backgroundColor || 'transparent'),
      borderRadius: container.style?.borderRadius || '4px',
      padding: container.style?.padding || '8px',
    }

    // 根据容器类型添加布局样式
    switch (container.type) {
      case CONTAINER_TYPES.FLEX:
        return {
          ...baseStyle,
          display: 'flex',
          flexDirection: container.flexDirection || 'row',
          justifyContent: container.justifyContent || 'flex-start',
          alignItems: container.alignItems || 'flex-start',
          flexWrap: container.flexWrap || 'nowrap',
          gap: container.gap || '8px',
        }
      case CONTAINER_TYPES.GRID:
        return {
          ...baseStyle,
          display: 'grid',
          gridTemplateColumns: `repeat(${container.gridCols || 3}, 1fr)`,
          gridTemplateRows: container.gridRows ? `repeat(${container.gridRows}, 1fr)` : 'auto',
          gap: container.gap || '8px',
        }
      case CONTAINER_TYPES.STACK:
        return {
          ...baseStyle,
          position: 'relative',
        }
      case CONTAINER_TYPES.FREE:
      default:
        return {
          ...baseStyle,
          position: 'relative',
        }
    }
  }

  // 渲染容器内的元素
  const renderElements = () => {
    if (!container.elements || container.elements.length === 0) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500 text-sm">
          拖拽元素到这个{container.type === CONTAINER_TYPES.STACK ? '堆叠' : '容器'}
        </div>
      )
    }

    return container.elements.map((element) => {
      // 为不同容器类型的元素添加特殊样式
      let elementWrapper = null
      
      switch (container.type) {
        case CONTAINER_TYPES.GRID:
          elementWrapper = (
            <div
              key={element.id}
              style={{
                gridRow: element.gridRow,
                gridColumn: element.gridCol,
              }}
            >
              <CanvasElement element={element} />
            </div>
          )
          break
        case CONTAINER_TYPES.FLEX:
          elementWrapper = (
            <div
              key={element.id}
              style={{
                order: element.flexOrder || 0,
                flex: element.flex || 'none',
              }}
            >
              <CanvasElement element={element} />
            </div>
          )
          break
        case CONTAINER_TYPES.STACK:
          elementWrapper = (
            <div
              key={element.id}
              style={{
                position: 'absolute',
                top: element.margin?.top || 0,
                left: element.margin?.left || 0,
                zIndex: element.zIndex || 1,
              }}
            >
              <CanvasElement element={element} />
            </div>
          )
          break
        case CONTAINER_TYPES.FREE:
        default:
          elementWrapper = <CanvasElement key={element.id} element={element} />
      }

      return elementWrapper
    })
  }

  const isSelected = selectedElements.includes(container.id)

  return (
    <div
      ref={(node) => {
        containerRef.current = node
        drop(node)
      }}
      data-container-id={container.id}
      className={`container-wrapper ${isSelected ? 'selected' : ''}`}
      style={getContainerStyle()}
      onClick={(e) => {
        e.stopPropagation()
        selectElement(container.id)
      }}
    >
      {/* 容器标题 */}
      {container.showTitle && (
        <div className="container-title text-xs text-gray-600 mb-2">
          {container.name || `${container.type === CONTAINER_TYPES.STACK ? '堆叠' : '容器'} ${container.id.slice(-4)}`}
        </div>
      )}
      
      {/* 容器内容 */}
      <div className="container-content">
        {renderElements()}
      </div>
    </div>
  )
}

export default Container
