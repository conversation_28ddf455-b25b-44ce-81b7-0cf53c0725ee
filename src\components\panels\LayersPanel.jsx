import React from 'react'
import { motion } from 'framer-motion'
import { Eye, EyeOff, Lock, Unlock, Trash2, ChevronUp, ChevronDown } from 'lucide-react'
import { useEditorStore } from '../../store/editorStore'

const LayerItem = ({ element, isSelected, onSelect, onToggleVisibility, onToggleLock, onDelete, onMoveUp, onMoveDown }) => {
  const getElementIcon = (type) => {
    const icons = {
      text: '📝',
      image: '🖼️',
      button: '🔘',
      container: '📦',
      form: '📋',
      video: '🎥',
      gallery: '🖼️',
      map: '🗺️',
    }
    return icons[type] || '📄'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all
        ${isSelected 
          ? 'bg-primary-50 border border-primary-200' 
          : 'hover:bg-gray-50 border border-transparent'
        }
        ${!element.visible ? 'opacity-50' : ''}
      `}
      onClick={onSelect}
    >
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <span className="text-lg">{getElementIcon(element.type)}</span>
        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate">
            {element.type === 'text' ? element.content?.slice(0, 20) + '...' : 
             element.type === 'image' ? '图片' :
             element.type === 'button' ? element.content || '按钮' :
             element.type}
          </div>
          <div className="text-xs text-gray-500">
            层级: {element.zIndex || 1} • {Math.round(element.margin?.left || 0)}, {Math.round(element.margin?.top || 0)}
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {/* 层级控制按钮 */}
        <button
          onClick={(e) => {
            e.stopPropagation()
            onMoveUp()
          }}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title="上移一层"
        >
          <ChevronUp className="w-3 h-3 text-gray-600" />
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation()
            onMoveDown()
          }}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title="下移一层"
        >
          <ChevronDown className="w-3 h-3 text-gray-600" />
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation()
            onToggleVisibility()
          }}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title={element.visible ? '隐藏' : '显示'}
        >
          {element.visible ? (
            <Eye className="w-4 h-4 text-gray-600" />
          ) : (
            <EyeOff className="w-4 h-4 text-gray-400" />
          )}
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation()
            onToggleLock()
          }}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title={element.locked ? '解锁' : '锁定'}
        >
          {element.locked ? (
            <Lock className="w-4 h-4 text-gray-600" />
          ) : (
            <Unlock className="w-4 h-4 text-gray-400" />
          )}
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation()
            onDelete()
          }}
          className="p-1 hover:bg-red-100 rounded transition-colors"
          title="删除"
        >
          <Trash2 className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </motion.div>
  )
}

const LayersPanel = () => {
  const {
    currentElements,
    selectedElement,
    selectElement,
    updateElement,
    deleteElement,
    bringToFront,
    sendToBack,
    moveForward,
    moveBackward,
  } = useEditorStore()

  const handleToggleVisibility = (elementId) => {
    const element = currentElements.find(el => el.id === elementId)
    if (element) {
      updateElement(elementId, { visible: !element.visible })
    }
  }

  const handleToggleLock = (elementId) => {
    const element = currentElements.find(el => el.id === elementId)
    if (element) {
      updateElement(elementId, { locked: !element.locked })
    }
  }

  const handleMoveUp = (elementId) => {
    moveForward(elementId)
  }

  const handleMoveDown = (elementId) => {
    moveBackward(elementId)
  }

  // 按 z-index 排序，最高的在最上面
  const sortedElements = [...currentElements].sort((a, b) => (b.zIndex || 1) - (a.zIndex || 1))

  return (
    <div className="p-6 h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-4">
        {/* 图层统计 */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>共 {currentElements.length} 个图层</span>
          <span>按 Z 轴排序</span>
        </div>

        {/* 图层列表 */}
        <div className="space-y-2">
          {sortedElements.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div className="text-gray-500 text-sm">
                暂无图层
              </div>
              <div className="text-gray-400 text-xs mt-1">
                从左侧添加元素开始创建
              </div>
            </div>
          ) : (
            sortedElements.map((element) => (
              <LayerItem
                key={element.id}
                element={element}
                isSelected={selectedElement === element.id}
                onSelect={() => selectElement(element.id)}
                onToggleVisibility={() => handleToggleVisibility(element.id)}
                onToggleLock={() => handleToggleLock(element.id)}
                onDelete={() => deleteElement(element.id)}
                onMoveUp={() => handleMoveUp(element.id)}
                onMoveDown={() => handleMoveDown(element.id)}
              />
            ))
          )}
        </div>

        {/* 图层操作提示 */}
        {currentElements.length > 0 && (
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">💡 图层操作</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 点击图层名称选中元素</li>
              <li>• 使用上下箭头调整层级</li>
              <li>• 使用眼睛图标控制显示/隐藏</li>
              <li>• 使用锁图标锁定/解锁元素</li>
              <li>• 图层按 Z 轴顺序排列（高层级在上）</li>
              <li>• 右键元素可快速访问层级菜单</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  )
}

export default LayersPanel
